const fs = require("fs");
const path = require("path");
const axios = require("axios");
const cheerio = require("cheerio");
const { HttpsProxyAgent } = require("https-proxy-agent");

// 代理配置（如果需要）
const USE_PROXY = true; // 是否使用代理
const PROXY_URL = "http://127.0.0.1:7890"; // 代理地址，根据您的实际代理设置修改
const proxyAgent = USE_PROXY ? new HttpsProxyAgent(PROXY_URL) : null;

// 最小图片大小（字节），用于检查图片质量
const MIN_ICON_SIZE = 100; // 100字节以下可能是占位符图片

// 读取工具数据
const dataPath = path.join(
  __dirname,
  "../miniprogram/data/数据20250427/tools_new_20250427.json"
);
const toolsData = JSON.parse(fs.readFileSync(dataPath, "utf8"));

// 确保图片目录存在
const baseImageDir = path.join(
  __dirname,
  "../miniprogram/data/数据20250427/图片"
);
if (!fs.existsSync(baseImageDir)) {
  fs.mkdirSync(baseImageDir, { recursive: true });
}

// 获取命令行传入的分类名称
const categoryArg = process.argv[2];

// 如果没有提供分类参数，显示可用分类列表
if (!categoryArg) {
  console.log("请指定要下载的分类。可用分类列表:");
  toolsData.categories.forEach((category) => {
    const toolsCount = toolsData.tools.filter(
      (tool) => tool.categoryId === category.id
    ).length;
    console.log(`  - ${category.id} (${toolsCount}个工具)`);
  });
  console.log('\n使用方法: node downloadIconsByCategory.js "分类名称"');
  console.log('例如: node downloadIconsByCategory.js "AI聊天"');
  process.exit(0);
}

// 检查分类是否存在
const selectedCategory = toolsData.categories.find((c) => c.id === categoryArg);
if (!selectedCategory) {
  console.log(`错误: 找不到分类 "${categoryArg}"`);
  process.exit(1);
}

// 为该分类创建目录
const categoryDir = path.join(baseImageDir, selectedCategory.id);
if (!fs.existsSync(categoryDir)) {
  fs.mkdirSync(categoryDir, { recursive: true });
}

// 判断一个URL是否需要代理（可以根据需要自定义规则）
function needsProxy(url) {
  // 这些域名通常需要代理
  const proxyDomains = [
    "openai.com",
    "chat.openai.com",
    "google.com",
    "gemini.google.com",
    "anthropic.com",
    "claude.ai",
    "x.ai",
    "poe.com",
    "deepai.org",
    "mistral.ai",
    "meta.ai",
    "facebook.com",
    "midjourney.com",
    "ideogram.ai",
    "brain.fm",
    "leonardo.ai",
  ];

  try {
    const hostname = new URL(url).hostname;
    return proxyDomains.some((domain) => hostname.includes(domain));
  } catch (e) {
    return false;
  }
}

// 检查图片是否有效（简单的大小检查）
function isValidIcon(imageBuffer) {
  if (!imageBuffer || imageBuffer.length < MIN_ICON_SIZE) {
    return false;
  }

  // 检查是否是PNG格式的文件头
  const pngHeader = Buffer.from([0x89, 0x50, 0x4e, 0x47]);
  if (imageBuffer.length >= 4 && imageBuffer.subarray(0, 4).equals(pngHeader)) {
    return true;
  }

  // 检查是否是JPEG格式的文件头
  const jpegHeader = Buffer.from([0xff, 0xd8, 0xff]);
  if (
    imageBuffer.length >= 3 &&
    imageBuffer.subarray(0, 3).equals(jpegHeader)
  ) {
    return true;
  }

  // 检查是否是ICO格式的文件头
  const icoHeader = Buffer.from([0x00, 0x00, 0x01, 0x00]);
  if (imageBuffer.length >= 4 && imageBuffer.subarray(0, 4).equals(icoHeader)) {
    return true;
  }

  return false;
}

// 下载图标的函数
async function downloadIcon(tool) {
  // 检查本地图标是否实际存在
  if (tool.localIcon) {
    const iconPath = path.join(
      __dirname,
      "../miniprogram/data/数据20250427/",
      tool.localIcon
    );
    if (fs.existsSync(iconPath)) {
      console.log(`✅ ${tool.name} 已有本地图标: ${tool.localIcon}`);
      return;
    } else {
      console.log(
        `⚠️ ${tool.name} 的本地图标路径存在，但文件不存在: ${tool.localIcon}`
      );
      // 如果文件不存在，继续执行下载逻辑
    }
  }

  // 从URL中提取域名
  let domain;
  try {
    domain = new URL(tool.url).hostname;
  } catch (e) {
    console.error(`❌ ${tool.name} 的URL格式无效: ${tool.url}`);
    return;
  }

  console.log(`\n🔄 开始下载 ${tool.name} 的图标...`);

  // 第1优先级：使用Google的Favicon服务（优化版本，使用sz=64）
  try {
    const googleFaviconUrl = `https://www.google.com/s2/favicons?domain=${domain}&sz=64`;
    console.log(`  📡 [方法1] 尝试Google Favicon服务: ${googleFaviconUrl}`);

    const axiosConfig = {
      timeout: 10000, // 10秒超时
      responseType: "arraybuffer",
    };

    if (USE_PROXY && proxyAgent) {
      axiosConfig.httpsAgent = proxyAgent;
      console.log(`  🌐 使用代理连接`);
    }

    // 下载图标
    const iconResponse = await axios({
      method: "get",
      url: googleFaviconUrl,
      ...axiosConfig,
    });

    // 检查图片质量
    if (isValidIcon(iconResponse.data)) {
      // 生成文件名和保存路径
      const fileName = `${tool.name.replace(/\s+/g, "_")}.png`;
      const categoryDir = path.join(baseImageDir, tool.categoryId);
      const filePath = path.join(categoryDir, fileName);

      // 保存文件
      fs.writeFileSync(filePath, iconResponse.data);

      // 更新工具数据中的localIcon字段
      tool.localIcon = `图片/${tool.categoryId}/${fileName}`;
      console.log(
        `  ✅ [方法1成功] Google Favicon - 大小: ${(
          iconResponse.data.length / 1024
        ).toFixed(1)}KB`
      );
      console.log(`  💾 保存路径: ${tool.localIcon}`);
      return;
    } else {
      console.log(
        `  ⚠️ [方法1] Google Favicon图片质量不符合要求，尝试下一种方法...`
      );
    }
  } catch (error) {
    console.log(`  ❌ [方法1失败] Google Favicon: ${error.message}`);
  }

  // 第2优先级：尝试使用favicon.im服务
  try {
    console.log(`  📡 [方法2] 尝试favicon.im服务...`);
    const faviconImUrl = `https://favicon.im/${domain}?larger=true`;

    const axiosConfig = {
      timeout: 10000, // 10秒超时
      responseType: "arraybuffer",
    };

    if (USE_PROXY && proxyAgent) {
      axiosConfig.httpsAgent = proxyAgent;
    }

    // 下载图标
    const iconResponse = await axios({
      method: "get",
      url: faviconImUrl,
      ...axiosConfig,
    });

    // 检查图片质量
    if (isValidIcon(iconResponse.data)) {
      // 生成文件名和保存路径
      const fileName = `${tool.name.replace(/\s+/g, "_")}.png`;
      const categoryDir = path.join(baseImageDir, tool.categoryId);
      const filePath = path.join(categoryDir, fileName);

      // 保存文件
      fs.writeFileSync(filePath, iconResponse.data);

      // 更新工具数据中的localIcon字段
      tool.localIcon = `图片/${tool.categoryId}/${fileName}`;
      console.log(
        `  ✅ [方法2成功] favicon.im - 大小: ${(
          iconResponse.data.length / 1024
        ).toFixed(1)}KB`
      );
      console.log(`  💾 保存路径: ${tool.localIcon}`);
      return;
    } else {
      console.log(
        `  ⚠️ [方法2] favicon.im图片质量不符合要求，尝试下一种方法...`
      );
    }
  } catch (faviconImError) {
    console.log(`  ❌ [方法2失败] favicon.im: ${faviconImError.message}`);
  }

  // 第3优先级：从网站直接获取favicon
  try {
    console.log(`  📡 [方法3] 尝试从网站直接获取favicon...`);

    // 确定是否需要为此URL使用代理
    const shouldUseProxy = USE_PROXY && needsProxy(tool.url);
    const axiosConfig = {
      timeout: 10000, // 10秒超时
    };

    if (shouldUseProxy && proxyAgent) {
      axiosConfig.httpsAgent = proxyAgent;
      console.log(`  🌐 使用代理访问网站`);
    }

    // 尝试从网站获取favicon
    const response = await axios.get(tool.url, axiosConfig);
    const $ = cheerio.load(response.data);

    // 查找favicon链接
    let iconUrl =
      $('link[rel="icon"]').attr("href") ||
      $('link[rel="shortcut icon"]').attr("href") ||
      $('link[rel="apple-touch-icon"]').attr("href");

    // 如果找不到明确的图标链接，使用默认favicon路径
    if (!iconUrl) {
      iconUrl = new URL("/favicon.ico", tool.url).toString();
    }

    // 处理相对URL
    if (iconUrl && !iconUrl.startsWith("http")) {
      iconUrl = new URL(iconUrl, tool.url).toString();
    }

    if (iconUrl) {
      console.log(`  🎯 找到favicon链接: ${iconUrl}`);

      // 下载图标，同样考虑代理
      const iconResponse = await axios({
        method: "get",
        url: iconUrl,
        responseType: "arraybuffer",
        timeout: 10000,
        ...(shouldUseProxy && proxyAgent ? { httpsAgent: proxyAgent } : {}),
      });

      // 检查图片质量
      if (isValidIcon(iconResponse.data)) {
        // 生成文件名和保存路径
        const fileName = `${tool.name.replace(/\s+/g, "_")}.png`;
        const categoryDir = path.join(baseImageDir, tool.categoryId);
        const filePath = path.join(categoryDir, fileName);

        // 保存文件
        fs.writeFileSync(filePath, iconResponse.data);

        // 更新工具数据中的localIcon字段
        tool.localIcon = `图片/${tool.categoryId}/${fileName}`;
        console.log(
          `  ✅ [方法3成功] 网站直接获取 - 大小: ${(
            iconResponse.data.length / 1024
          ).toFixed(1)}KB`
        );
        console.log(`  💾 保存路径: ${tool.localIcon}`);
        return;
      } else {
        console.log(`  ⚠️ [方法3] 网站favicon图片质量不符合要求`);
      }
    } else {
      console.log(`  ❌ [方法3] 无法在网站中找到favicon链接`);
    }
  } catch (fallbackError) {
    console.log(`  ❌ [方法3失败] 网站直接获取: ${fallbackError.message}`);
  }

  // 所有方法都失败了
  console.log(`  💥 ${tool.name} 所有下载方法都失败了`);
}

// 处理所选分类的所有工具
async function processCategory() {
  // 获取该分类下的所有工具
  const categoryTools = toolsData.tools.filter(
    (tool) => tool.categoryId === selectedCategory.id
  );
  console.log(
    `\n🚀 开始下载 "${selectedCategory.id}" 分类的图标 (共${categoryTools.length}个工具)...\n`
  );

  // 统计信息
  let successCount = 0;
  let failCount = 0;
  let skippedCount = 0;

  for (const tool of categoryTools) {
    try {
      // 检查本地图标是否已存在
      if (tool.localIcon) {
        const iconPath = path.join(
          __dirname,
          "../miniprogram/data/数据20250427/",
          tool.localIcon
        );
        if (fs.existsSync(iconPath)) {
          skippedCount++;
          console.log(`⏭️ 跳过 ${tool.name}: 已有图标`);
          continue;
        }
      }

      await downloadIcon(tool);

      // 检查下载后是否有localIcon，有则表示成功
      if (tool.localIcon) {
        const iconPath = path.join(
          __dirname,
          "../miniprogram/data/数据20250427/",
          tool.localIcon
        );
        if (fs.existsSync(iconPath)) {
          successCount++;
        } else {
          failCount++;
        }
      } else {
        failCount++;
      }

      // 添加延迟以避免请求过快
      await new Promise((resolve) => setTimeout(resolve, 1000));
    } catch (error) {
      console.error(`💥 处理 ${tool.name} 时发生错误:`, error.message);
      failCount++;
    }
  }

  // 保存更新后的数据
  fs.writeFileSync(dataPath, JSON.stringify(toolsData, null, 2), "utf8");

  console.log(`
═══════════════════════════════════════
🎯 下载完成! "${selectedCategory.id}" 分类统计:
═══════════════════════════════════════
✅ 成功下载: ${successCount} 个
❌ 下载失败: ${failCount} 个  
⏭️ 已有图标: ${skippedCount} 个
📊 分类工具总数: ${categoryTools.length} 个
🎉 成功率: ${((successCount / (successCount + failCount)) * 100).toFixed(1)}%
═══════════════════════════════════════
`);
}

processCategory();
