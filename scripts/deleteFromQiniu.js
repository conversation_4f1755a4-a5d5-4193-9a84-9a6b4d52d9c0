/**
 * 七牛云数据删除脚本
 * 删除七牛云存储空间中的指定文件或文件夹
 */

const qiniu = require("qiniu");

// 七牛云配置（与上传脚本保持一致）
const qiniuConfig = {
  ACCESS_KEY: "oZsUmpCeCCSL24rbYsH1g3YW6hLMojhEGp4Ua0cb",
  SECRET_KEY: "UhTlav9aMpjGfrLK3kDLGCnvwe8FfN9KNQ1bNmHy",
  BUCKET: "naixi-ai-tools",
  DOMAIN: "naixi.qiniu.logitnote.com",
};

// 数据路径配置
const DATA_CONFIG = {
  DATA_VERSION: "20250427",
  QINIU_BASE_PATH: "数据20250427", // 要删除的基础路径
};

// 初始化七牛云配置
const mac = new qiniu.auth.digest.Mac(
  qiniuConfig.ACCESS_KEY,
  qiniuConfig.SECRET_KEY
);

const config = new qiniu.conf.Config();
config.zone = qiniu.zone.Zone_z2; // 华南区域
const bucketManager = new qiniu.rs.BucketManager(mac, config);

/**
 * 列出指定前缀的所有文件
 * @param {string} prefix - 文件前缀
 * @returns {Promise<Array>} 文件列表
 */
function listFiles(prefix = "") {
  return new Promise((resolve, reject) => {
    const options = {
      limit: 1000, // 每次最多获取1000个文件
      prefix: prefix, // 文件前缀
    };

    console.log(`📋 正在列出前缀为 "${prefix}" 的文件...`);

    bucketManager.listPrefix(
      qiniuConfig.BUCKET,
      options,
      (err, respBody, respInfo) => {
        if (err) {
          console.error("❌ 列出文件失败:", err);
          reject(err);
          return;
        }

        if (respInfo.statusCode === 200) {
          const items = respBody.items || [];
          console.log(`✅ 找到 ${items.length} 个文件`);
          resolve(items);
        } else {
          console.error("❌ 列出文件失败:", respInfo.statusCode, respBody);
          reject(new Error(`列出文件失败: ${respInfo.statusCode}`));
        }
      }
    );
  });
}

/**
 * 删除单个文件
 * @param {string} key - 文件键名
 * @returns {Promise<boolean>} 是否删除成功
 */
function deleteFile(key) {
  return new Promise((resolve, reject) => {
    console.log(`🗑️  正在删除: ${key}`);

    bucketManager.delete(qiniuConfig.BUCKET, key, (err, respBody, respInfo) => {
      if (err) {
        console.error(`❌ 删除失败: ${key}`, err);
        reject(err);
        return;
      }

      if (respInfo.statusCode === 200) {
        console.log(`✅ 删除成功: ${key}`);
        resolve(true);
      } else {
        console.error(`❌ 删除失败: ${key}`, respInfo.statusCode, respBody);
        resolve(false);
      }
    });
  });
}

/**
 * 批量删除文件
 * @param {Array} fileKeys - 文件键名数组
 * @returns {Promise<Object>} 删除结果统计
 */
async function batchDeleteFiles(fileKeys) {
  console.log(`\n🗑️  开始批量删除 ${fileKeys.length} 个文件...`);

  let successCount = 0;
  let failCount = 0;
  const results = [];

  for (const key of fileKeys) {
    try {
      const success = await deleteFile(key);
      if (success) {
        successCount++;
      } else {
        failCount++;
      }
      results.push({ key, success });

      // 添加小延迟，避免请求过快
      await new Promise((resolve) => setTimeout(resolve, 100));
    } catch (error) {
      console.error(`❌ 删除文件异常: ${key}`, error.message);
      failCount++;
      results.push({ key, success: false, error: error.message });
    }
  }

  console.log(`\n📊 删除完成统计:`);
  console.log(`   成功: ${successCount} 个`);
  console.log(`   失败: ${failCount} 个`);
  console.log(`   总计: ${fileKeys.length} 个`);

  return {
    total: fileKeys.length,
    success: successCount,
    failed: failCount,
    results,
  };
}

/**
 * 删除指定前缀的所有文件
 * @param {string} prefix - 文件前缀
 * @returns {Promise<Object>} 删除结果
 */
async function deleteByPrefix(prefix) {
  try {
    // 1. 列出所有文件
    const files = await listFiles(prefix);

    if (files.length === 0) {
      console.log(`📭 没有找到前缀为 "${prefix}" 的文件`);
      return { total: 0, success: 0, failed: 0, results: [] };
    }

    // 2. 提取文件键名
    const fileKeys = files.map((file) => file.key);

    console.log(`\n📋 将要删除的文件列表:`);
    fileKeys.forEach((key, index) => {
      console.log(`   ${index + 1}. ${key}`);
    });

    // 3. 确认删除
    console.log(`\n⚠️  即将删除 ${fileKeys.length} 个文件，请确认！`);

    // 4. 批量删除
    const result = await batchDeleteFiles(fileKeys);

    return result;
  } catch (error) {
    console.error("❌ 删除操作失败:", error);
    throw error;
  }
}

/**
 * 主删除流程
 */
async function main() {
  console.log("🗑️  开始删除七牛云数据...");
  console.log(`📊 配置信息:`);
  console.log(`   存储空间: ${qiniuConfig.BUCKET}`);
  console.log(`   CDN域名: ${qiniuConfig.DOMAIN}`);
  console.log(`   删除前缀: ${DATA_CONFIG.QINIU_BASE_PATH}`);

  try {
    // 删除指定前缀的所有文件
    const result = await deleteByPrefix(DATA_CONFIG.QINIU_BASE_PATH);

    console.log("\n🎉 删除完成总结:");
    console.log(`   总文件数: ${result.total}`);
    console.log(`   成功删除: ${result.success}`);
    console.log(`   删除失败: ${result.failed}`);

    if (result.failed > 0) {
      console.log("\n❌ 删除失败的文件:");
      result.results
        .filter((r) => !r.success)
        .forEach((r, index) => {
          console.log(`   ${index + 1}. ${r.key} - ${r.error || "未知错误"}`);
        });
    }
  } catch (error) {
    console.error("❌ 删除流程失败:", error);
    process.exit(1);
  }
}

// 检查配置
function checkConfig() {
  const requiredKeys = ["ACCESS_KEY", "SECRET_KEY", "BUCKET", "DOMAIN"];
  const missingKeys = requiredKeys.filter(
    (key) => !qiniuConfig[key] || qiniuConfig[key].startsWith("your_")
  );

  if (missingKeys.length > 0) {
    console.error("❌ 请先配置七牛云参数:");
    missingKeys.forEach((key) => {
      console.error(`   ${key}: ${qiniuConfig[key]}`);
    });
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  checkConfig();
  main().catch(console.error);
}

module.exports = {
  listFiles,
  deleteFile,
  batchDeleteFiles,
  deleteByPrefix,
};
