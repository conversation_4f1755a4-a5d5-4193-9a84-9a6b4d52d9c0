const fs = require("fs");
const path = require("path");

// 读取工具数据
const dataPath = path.join(
  __dirname,
  "../miniprogram/data/数据20250427/tools_new_20250427.json"
);
const toolsData = JSON.parse(fs.readFileSync(dataPath, "utf8"));

// 统计数据
let updatedCount = 0;
let missingIconsCount = 0;
let existingIconsCount = 0;
let fixedPathsCount = 0;

toolsData.tools.forEach((tool) => {
  // 如果已有localIcon路径，检查文件是否真实存在
  if (tool.localIcon) {
    const fullPath = path.join(
      __dirname,
      "../miniprogram/data/数据20250427/",
      tool.localIcon
    );

    if (fs.existsSync(fullPath)) {
      // 文件存在，跳过处理
      existingIconsCount++;
    } else {
      // 文件不存在，清除无效路径并尝试查找正确路径
      console.log(`发现无效路径: ${tool.name} -> ${tool.localIcon}`);
      const suggestedPath = `图片/${tool.categoryId}/${tool.name.replace(
        /\s+/g,
        "_"
      )}.png`;
      const suggestedFullPath = path.join(
        __dirname,
        "../miniprogram/data/数据20250427/",
        suggestedPath
      );

      if (fs.existsSync(suggestedFullPath)) {
        // 找到了正确路径
        tool.localIcon = suggestedPath;
        fixedPathsCount++;
        console.log(`已修复路径: ${tool.name} -> ${suggestedPath}`);
      } else {
        // 路径无效且无替代路径，清除它
        tool.localIcon = "";
        missingIconsCount++;
        console.log(`移除无效路径: ${tool.name}`);
      }
    }
  }

  // 对于没有localIcon的工具，检查是否有对应图片
  if (!tool.localIcon) {
    const suggestedPath = `图片/${tool.categoryId}/${tool.name.replace(
      /\s+/g,
      "_"
    )}.png`;
    const fullPath = path.join(
      __dirname,
      "../miniprogram/data/数据20250427/",
      suggestedPath
    );

    if (fs.existsSync(fullPath)) {
      tool.localIcon = suggestedPath;
      updatedCount++;
      console.log(`已更新: ${tool.name} -> ${suggestedPath}`);
    } else {
      missingIconsCount++;
    }
  }
});

// 保存更新后的数据
fs.writeFileSync(dataPath, JSON.stringify(toolsData, null, 2), "utf8");

// 输出统计信息
console.log(`
操作完成! 统计信息:
  - 发现有效图标: ${existingIconsCount}
  - 修复无效路径: ${fixedPathsCount}
  - 新增图标路径: ${updatedCount}
  - 仍缺少图标: ${missingIconsCount}
  - 工具总数: ${toolsData.tools.length}
`);
