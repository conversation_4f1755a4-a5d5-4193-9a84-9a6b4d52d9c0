const fs = require("fs");
const path = require("path");

// 读取工具数据
const dataPath = path.join(
  __dirname,
  "../miniprogram/data/数据20250427/tools_new_20250427.json"
);
const toolsData = JSON.parse(fs.readFileSync(dataPath, "utf8"));

// 确保图片目录存在
const baseImageDir = path.join(
  __dirname,
  "../miniprogram/data/数据20250427/图片"
);
if (!fs.existsSync(baseImageDir)) {
  fs.mkdirSync(baseImageDir, { recursive: true });
}

// 为每个分类创建目录
toolsData.categories.forEach((category) => {
  const categoryDir = path.join(baseImageDir, category.id);
  if (!fs.existsSync(categoryDir)) {
    fs.mkdirSync(categoryDir, { recursive: true });
  }
});

// 生成待下载列表
const missingIcons = [];
let foundIcons = 0;

toolsData.tools.forEach((tool) => {
  if (!tool.localIcon) {
    missingIcons.push({
      id: tool.id,
      name: tool.name,
      url: tool.url,
      category: tool.categoryId,
      suggestedFileName: `${tool.name.replace(/\s+/g, "_")}.png`,
      suggestedPath: `图片/${tool.categoryId}/${tool.name.replace(
        /\s+/g,
        "_"
      )}.png`,
    });
  } else {
    foundIcons++;
  }
});

// 将列表保存为CSV文件，方便手动下载
const csvContent =
  "ID,名称,网址,分类,建议文件名,建议路径\n" +
  missingIcons
    .map(
      (item) =>
        `${item.id},${item.name},${item.url},${item.category},${item.suggestedFileName},${item.suggestedPath}`
    )
    .join("\n");

fs.writeFileSync(path.join(__dirname, "missing_icons.csv"), csvContent);

// 生成更新数据的脚本
const updateScript = `
const fs = require('fs');
const path = require('path');

// 读取工具数据
const dataPath = path.join(__dirname, '../miniprogram/data/数据20250427/tools_new_20250427.json');
const toolsData = JSON.parse(fs.readFileSync(dataPath, 'utf8'));

// 更新localIcon字段
let updatedCount = 0;
toolsData.tools.forEach(tool => {
  if (!tool.localIcon) {
    const suggestedPath = \`图片/\${tool.categoryId}/\${tool.name.replace(/\\s+/g, '_')}.png\`;
    const fullPath = path.join(__dirname, '../miniprogram/data/数据20250427/', suggestedPath);
    
    if (fs.existsSync(fullPath)) {
      tool.localIcon = suggestedPath;
      updatedCount++;
      console.log(\`已更新: \${tool.name} -> \${suggestedPath}\`);
    }
  }
});

// 保存更新后的数据
fs.writeFileSync(dataPath, JSON.stringify(toolsData, null, 2), 'utf8');
console.log(\`完成! 更新了 \${updatedCount} 个工具的图标路径\`);
`;

fs.writeFileSync(path.join(__dirname, "updateIconPaths.js"), updateScript);

console.log(`
图标状态统计:
  - 已有图标: ${foundIcons}
  - 缺少图标: ${missingIcons.length}
  - 总工具数: ${toolsData.tools.length}

已创建必要的目录结构，并生成以下文件:
  - missing_icons.csv: 包含需要下载图标的工具列表
  - updateIconPaths.js: 下载完成后运行此脚本更新数据

使用步骤:
1. 查看missing_icons.csv
2. 手动下载每个工具的图标 
3. 将图标保存到对应的建议路径
4. 运行 'node updateIconPaths.js' 更新数据
`);
