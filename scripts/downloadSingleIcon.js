const fs = require("fs");
const path = require("path");
const axios = require("axios");
const cheerio = require("cheerio");
const { HttpsProxyAgent } = require("https-proxy-agent");

// 代理配置（如果需要）
const USE_PROXY = true; // 是否使用代理
const PROXY_URL = "http://127.0.0.1:7890"; // 代理地址，根据您的实际代理设置修改
const proxyAgent = USE_PROXY ? new HttpsProxyAgent(PROXY_URL) : null;

// 最小图片大小（字节），用于检查图片质量
const MIN_ICON_SIZE = 100; // 100字节以下可能是占位符图片

// 读取工具数据
const dataPath = path.join(
  __dirname,
  "../miniprogram/data/数据20250427/tools_new_20250427.json"
);
const toolsData = JSON.parse(fs.readFileSync(dataPath, "utf8"));

// 确保图片目录存在
const baseImageDir = path.join(
  __dirname,
  "../miniprogram/data/数据20250427/图片"
);
if (!fs.existsSync(baseImageDir)) {
  fs.mkdirSync(baseImageDir, { recursive: true });
}

// 获取命令行传入的工具名称
const toolNameArg = process.argv[2];
const forceDownload =
  process.argv.includes("--force") || process.argv.includes("-f");

// 如果没有提供工具参数，显示可用工具列表
if (!toolNameArg) {
  console.log("请指定要下载图标的工具名称。可用工具列表:");
  console.log("═".repeat(60));

  // 按分类显示所有工具
  toolsData.categories.forEach((category) => {
    const categoryTools = toolsData.tools.filter(
      (tool) => tool.categoryId === category.id
    );

    if (categoryTools.length > 0) {
      console.log(`\n📁 ${category.id}:`);
      categoryTools.forEach((tool, index) => {
        const hasIcon = tool.localIcon ? "✅" : "❌";
        console.log(`  ${index + 1}. ${tool.name} ${hasIcon}`);
      });
    }
  });

  console.log("\n═".repeat(60));
  console.log('使用方法: node downloadSingleIcon.js "工具名称" [--force]');
  console.log('例如: node downloadSingleIcon.js "ChatGPT"');
  console.log(
    '     node downloadSingleIcon.js "ChatGPT" --force  # 强制重新下载'
  );
  console.log("提示: ✅=已有图标 ❌=缺少图标");
  process.exit(0);
}

// 查找指定的工具（支持模糊匹配）
const foundTools = toolsData.tools.filter((tool) =>
  tool.name.toLowerCase().includes(toolNameArg.toLowerCase())
);

if (foundTools.length === 0) {
  console.log(`❌ 错误: 找不到工具 "${toolNameArg}"`);
  console.log(
    "💡 提示: 请检查工具名称拼写，或使用不带参数的命令查看可用工具列表"
  );
  process.exit(1);
}

if (foundTools.length > 1) {
  console.log(`🔍 找到多个匹配的工具，请选择具体的工具名称:`);
  foundTools.forEach((tool, index) => {
    const hasIcon = tool.localIcon ? "✅" : "❌";
    console.log(`  ${index + 1}. ${tool.name} (${tool.categoryId}) ${hasIcon}`);
  });
  console.log("\n请使用完整的工具名称重新运行命令");
  process.exit(1);
}

const selectedTool = foundTools[0];

// 判断一个URL是否需要代理（可以根据需要自定义规则）
function needsProxy(url) {
  // 这些域名通常需要代理
  const proxyDomains = [
    "openai.com",
    "chat.openai.com",
    "google.com",
    "gemini.google.com",
    "anthropic.com",
    "claude.ai",
    "x.ai",
    "poe.com",
    "deepai.org",
    "mistral.ai",
    "meta.ai",
    "facebook.com",
    "midjourney.com",
    "ideogram.ai",
    "brain.fm",
    "leonardo.ai",
  ];

  try {
    const hostname = new URL(url).hostname;
    return proxyDomains.some((domain) => hostname.includes(domain));
  } catch (e) {
    return false;
  }
}

// 检查图片是否有效（简单的大小检查）
function isValidIcon(imageBuffer) {
  if (!imageBuffer || imageBuffer.length < MIN_ICON_SIZE) {
    return false;
  }

  // 检查是否是PNG格式的文件头
  const pngHeader = Buffer.from([0x89, 0x50, 0x4e, 0x47]);
  if (imageBuffer.length >= 4 && imageBuffer.subarray(0, 4).equals(pngHeader)) {
    return true;
  }

  // 检查是否是JPEG格式的文件头
  const jpegHeader = Buffer.from([0xff, 0xd8, 0xff]);
  if (
    imageBuffer.length >= 3 &&
    imageBuffer.subarray(0, 3).equals(jpegHeader)
  ) {
    return true;
  }

  // 检查是否是ICO格式的文件头
  const icoHeader = Buffer.from([0x00, 0x00, 0x01, 0x00]);
  if (imageBuffer.length >= 4 && imageBuffer.subarray(0, 4).equals(icoHeader)) {
    return true;
  }

  return false;
}

// 下载图标的函数
async function downloadIcon(tool) {
  // 为该分类创建目录
  const categoryDir = path.join(baseImageDir, tool.categoryId);
  if (!fs.existsSync(categoryDir)) {
    fs.mkdirSync(categoryDir, { recursive: true });
  }

  // 检查本地图标是否实际存在
  if (tool.localIcon) {
    const iconPath = path.join(
      __dirname,
      "../miniprogram/data/数据20250427/",
      tool.localIcon
    );
    if (fs.existsSync(iconPath)) {
      console.log(`✅ ${tool.name} 已有本地图标: ${tool.localIcon}`);
      const stats = fs.statSync(iconPath);
      console.log(`📊 当前图标大小: ${(stats.size / 1024).toFixed(1)}KB`);

      if (!forceDownload) {
        console.log("🔄 如需重新下载，请使用 --force 参数");
        console.log("🚫 取消下载，保留现有图标");
        return true; // 返回true表示已有图标，算作成功
      } else {
        console.log("🔥 强制重新下载模式，将替换现有图标");
      }
    } else {
      console.log(
        `⚠️ ${tool.name} 的本地图标路径存在，但文件不存在: ${tool.localIcon}`
      );
      // 如果文件不存在，继续执行下载逻辑
    }
  }

  // 从URL中提取域名
  let domain;
  try {
    domain = new URL(tool.url).hostname;
  } catch (e) {
    console.error(`❌ ${tool.name} 的URL格式无效: ${tool.url}`);
    return;
  }

  console.log(`\n🚀 开始下载 ${tool.name} 的图标...`);
  console.log(`🌐 工具网址: ${tool.url}`);
  console.log(`📁 分类: ${tool.categoryId}`);

  // 第1优先级：使用Google的Favicon服务（优化版本，使用sz=64）
  try {
    const googleFaviconUrl = `https://www.google.com/s2/favicons?domain=${domain}&sz=64`;
    console.log(`  📡 [方法1] 尝试Google Favicon服务: ${googleFaviconUrl}`);

    const axiosConfig = {
      timeout: 10000, // 10秒超时
      responseType: "arraybuffer",
    };

    if (USE_PROXY && proxyAgent) {
      axiosConfig.httpsAgent = proxyAgent;
      console.log(`  🌐 使用代理连接`);
    }

    // 下载图标
    const iconResponse = await axios({
      method: "get",
      url: googleFaviconUrl,
      ...axiosConfig,
    });

    // 检查图片质量
    if (isValidIcon(iconResponse.data)) {
      // 生成文件名和保存路径
      const fileName = `${tool.name.replace(/\s+/g, "_")}.png`;
      const filePath = path.join(categoryDir, fileName);

      // 保存文件
      fs.writeFileSync(filePath, iconResponse.data);

      // 更新工具数据中的localIcon字段
      tool.localIcon = `图片/${tool.categoryId}/${fileName}`;
      console.log(
        `  ✅ [方法1成功] Google Favicon - 大小: ${(
          iconResponse.data.length / 1024
        ).toFixed(1)}KB`
      );
      console.log(`  💾 保存路径: ${tool.localIcon}`);
      return true;
    } else {
      console.log(
        `  ⚠️ [方法1] Google Favicon图片质量不符合要求，尝试下一种方法...`
      );
    }
  } catch (error) {
    console.log(`  ❌ [方法1失败] Google Favicon: ${error.message}`);
  }

  // 第2优先级：尝试使用favicon.im服务
  try {
    console.log(`  📡 [方法2] 尝试favicon.im服务...`);
    const faviconImUrl = `https://favicon.im/${domain}?larger=true`;

    const axiosConfig = {
      timeout: 10000, // 10秒超时
      responseType: "arraybuffer",
    };

    if (USE_PROXY && proxyAgent) {
      axiosConfig.httpsAgent = proxyAgent;
    }

    // 下载图标
    const iconResponse = await axios({
      method: "get",
      url: faviconImUrl,
      ...axiosConfig,
    });

    // 检查图片质量
    if (isValidIcon(iconResponse.data)) {
      // 生成文件名和保存路径
      const fileName = `${tool.name.replace(/\s+/g, "_")}.png`;
      const filePath = path.join(categoryDir, fileName);

      // 保存文件
      fs.writeFileSync(filePath, iconResponse.data);

      // 更新工具数据中的localIcon字段
      tool.localIcon = `图片/${tool.categoryId}/${fileName}`;
      console.log(
        `  ✅ [方法2成功] favicon.im - 大小: ${(
          iconResponse.data.length / 1024
        ).toFixed(1)}KB`
      );
      console.log(`  💾 保存路径: ${tool.localIcon}`);
      return true;
    } else {
      console.log(
        `  ⚠️ [方法2] favicon.im图片质量不符合要求，尝试下一种方法...`
      );
    }
  } catch (faviconImError) {
    console.log(`  ❌ [方法2失败] favicon.im: ${faviconImError.message}`);
  }

  // 第3优先级：从网站直接获取favicon
  try {
    console.log(`  📡 [方法3] 尝试从网站直接获取favicon...`);

    // 确定是否需要为此URL使用代理
    const shouldUseProxy = USE_PROXY && needsProxy(tool.url);
    const axiosConfig = {
      timeout: 10000, // 10秒超时
    };

    if (shouldUseProxy && proxyAgent) {
      axiosConfig.httpsAgent = proxyAgent;
      console.log(`  🌐 使用代理访问网站`);
    }

    // 尝试从网站获取favicon
    const response = await axios.get(tool.url, axiosConfig);
    const $ = cheerio.load(response.data);

    // 查找favicon链接
    let iconUrl =
      $('link[rel="icon"]').attr("href") ||
      $('link[rel="shortcut icon"]').attr("href") ||
      $('link[rel="apple-touch-icon"]').attr("href");

    // 如果找不到明确的图标链接，使用默认favicon路径
    if (!iconUrl) {
      iconUrl = new URL("/favicon.ico", tool.url).toString();
    }

    // 处理相对URL
    if (iconUrl && !iconUrl.startsWith("http")) {
      iconUrl = new URL(iconUrl, tool.url).toString();
    }

    if (iconUrl) {
      console.log(`  🎯 找到favicon链接: ${iconUrl}`);

      // 下载图标，同样考虑代理
      const iconResponse = await axios({
        method: "get",
        url: iconUrl,
        responseType: "arraybuffer",
        timeout: 10000,
        ...(shouldUseProxy && proxyAgent ? { httpsAgent: proxyAgent } : {}),
      });

      // 检查图片质量
      if (isValidIcon(iconResponse.data)) {
        // 生成文件名和保存路径
        const fileName = `${tool.name.replace(/\s+/g, "_")}.png`;
        const filePath = path.join(categoryDir, fileName);

        // 保存文件
        fs.writeFileSync(filePath, iconResponse.data);

        // 更新工具数据中的localIcon字段
        tool.localIcon = `图片/${tool.categoryId}/${fileName}`;
        console.log(
          `  ✅ [方法3成功] 网站直接获取 - 大小: ${(
            iconResponse.data.length / 1024
          ).toFixed(1)}KB`
        );
        console.log(`  💾 保存路径: ${tool.localIcon}`);
        return true;
      } else {
        console.log(`  ⚠️ [方法3] 网站favicon图片质量不符合要求`);
      }
    } else {
      console.log(`  ❌ [方法3] 无法在网站中找到favicon链接`);
    }
  } catch (fallbackError) {
    console.log(`  ❌ [方法3失败] 网站直接获取: ${fallbackError.message}`);
  }

  // 所有方法都失败了
  console.log(`  💥 ${tool.name} 所有下载方法都失败了`);
  return false;
}

// 主处理函数
async function processSingleTool() {
  console.log(`\n🎯 准备下载 "${selectedTool.name}" 的图标...`);
  console.log(`📁 分类: ${selectedTool.categoryId}`);
  console.log(`🌐 网址: ${selectedTool.url}`);

  if (forceDownload) {
    console.log(`🔥 强制下载模式已启用`);
  }

  const result = await downloadIcon(selectedTool);

  if (result) {
    // 保存更新后的数据
    fs.writeFileSync(dataPath, JSON.stringify(toolsData, null, 2), "utf8");

    console.log(`
═══════════════════════════════════════
🎉 处理完成! "${selectedTool.name}" 图标处理成功!
═══════════════════════════════════════
✅ 工具名称: ${selectedTool.name}
📁 所属分类: ${selectedTool.categoryId}
💾 图标路径: ${selectedTool.localIcon || "无"}
🌐 工具网址: ${selectedTool.url}
═══════════════════════════════════════
`);
  } else {
    console.log(`
═══════════════════════════════════════
❌ 下载失败! "${selectedTool.name}" 图标下载失败
═══════════════════════════════════════
🔧 建议解决方案:
1. 检查网络连接
2. 确认代理设置是否正确
3. 尝试访问工具官网确认可访问性
4. 手动为该工具准备图标文件
5. 尝试使用 --force 参数强制重新下载
═══════════════════════════════════════
`);
  }
}

processSingleTool();
