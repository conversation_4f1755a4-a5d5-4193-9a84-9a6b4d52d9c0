{"categories": [{"id": "AI聊天", "name": "AI聊天", "order": 1}, {"id": "AI智能体", "name": "AI智能体", "order": 2}, {"id": "AI搜索", "name": "AI搜索", "order": 3}, {"id": "AI图像", "name": "AI图像", "order": 4}, {"id": "AI视频", "name": "AI视频", "order": 5}, {"id": "AI设计", "name": "AI设计", "order": 6}, {"id": "AI写作", "name": "AI写作", "order": 7}, {"id": "AI音频", "name": "AI音频", "order": 8}, {"id": "AI办公", "name": "AI办公", "order": 9}, {"id": "AI编程开发", "name": "AI编程开发", "order": 10}], "tools": [{"id": "chat_001", "name": "ChatGPT", "description": "ChatGPT是由美国OpenAI公司于2022年11月推出的生成式人工智能聊天机器人。它基于强大的GPT系列大型语言模型（目前主力为GPT-4o），具备卓越的自然语言理解和生成能力，以及多模态处理能力，可处理文本、图像、音频等多种格式信息。ChatGPT的核心功能涵盖人类风格对话、文本创作、代码辅助、语言翻译、知识问答和内容生成。其典型应用场景极其广泛，包括客服自动化、内容创作、教育辅导、程序开发、医疗咨询等。OpenAI平台也提供API，支持开发者将ChatGPT集成到各类应用中。ChatGPT已成为全球用户规模最大、影响最深远的AI应用之一，正引领新一轮AI技术浪潮。", "url": "https://chat.openai.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/ChatGPT.png", "categoryId": "AI聊天", "tags": ["魔法"], "localIcon": "图片/AI聊天/ChatGPT.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/ChatGPT.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI聊天/ChatGPT.png"}, {"id": "chat_002", "name": "DeepSeek", "description": "DeepSeek AI是由中国新锐AI公司DeepSeek开发的开源大模型系列，专为开发者、内容创作者及研究人员打造。该公司由浙江大学校友梁文锋于2023年5月在杭州创立，作为量化对冲基金High-Flyer旗下的独立AI研究实验室。DeepSeek主打透明推理过程、灵活定制与社区化开发，其旗舰模型如Deepseek-V2、Deepseek-Coder等，具备高级自然语言理解与生成、英中双语支持、复杂数据分析和强大编程辅助功能。DeepSeek的开源和免费政策极具吸引力，开发者可通过Web、API、本地部署深度集成，并支持商业用途。其低成本和开源策略对AI市场产生了显著影响，尽管存在数据隐私和审查等潜在风险。", "url": "https://www.deepseek.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/DeepSeek.png", "categoryId": "AI聊天", "tags": [], "localIcon": "图片/AI聊天/DeepSeek.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/DeepSeek.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI聊天/DeepSeek.png"}, {"id": "chat_003", "name": "<PERSON>", "description": "Claude是由美国人工智能初创公司Anthropic开发的一系列AI助手。Anthropic由前OpenAI员工于2021年创立，专注于构建安全、有益且可靠的AI系统。Claude模型系列包括Haicu、Sonnet和Opus等，旨在处理从轻量级任务到复杂分析、长文档处理和高级推理的各种工作负载。最新发布的Claude 3.5 Sonnet在编码和Agentic任务方面有显著提升，并引入了\"Computer Use\"功能，允许Claude像人一样交互使用电脑。Claude具备强大的多模态能力，支持文本、图像和代码的处理与生成，广泛应用于客户支持、内容审核、法律摘要、应用开发辅助和学术研究等领域。", "url": "https://claude.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/Claude.png", "categoryId": "AI聊天", "tags": ["魔法"], "localIcon": "图片/AI聊天/Claude.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/Claude.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI聊天/Claude.png"}, {"id": "chat_004", "name": "Gemini", "description": "Google Gemini是由Google DeepMind团队开发的先进多模态大语言模型，旨在成为Google最强大、最通用的AI基础模型。自2023年推出实验性产品Bard后，Gemini逐渐发展壮大，能原生处理和理解文本、图片、音频、视频和代码等多种数据类型。Gemini模型分为Ultra、Pro和Nano三种规模，分别适用于云端高复杂度任务、通用场景和本地端实时任务。它在多项主流学术基准测试中表现出色，尤其在多任务语言理解和跨模态推理方面。Gemini的核心应用广泛融入Google产品（如Google Workspace、Google Cloud、Pixel手机），并应用于办公协作、客服、营销、代码开发以及制造、金融、医疗等多个行业，推动企业级创新。", "url": "https://gemini.google.com/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/Gemini.png", "categoryId": "AI聊天", "tags": ["魔法"], "localIcon": "图片/AI聊天/Gemini.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/Gemini.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI聊天/Gemini.png"}, {"id": "chat_005", "name": "通义千问", "description": "通义千问是由阿里巴巴旗下阿里云自主研发的超大规模多模态大语言模型体系。该模型基于万亿级大规模数据训练，采用增强的Transformer结构、闪光注意力等技术，兼具强大的语言理解和生成能力，支持文本、语音、图像、视频等多模态输入处理。通义千问包含多个系列模型（Plus、Turbo、Long、VL、Audio），满足不同性能、速度和成本需求，并支持多种语言和长上下文理解。通义千问定位于企业级全方位AI解决方案的核心引擎，广泛应用于文本创作、问答检索、翻译、编程辅助、对话模拟、数据可视化、智能客服和电商图像搜索等场景，通过API和多端应用面向开发者和终端用户。", "url": "https://tongyi.aliyun.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/通义千问.png", "categoryId": "AI聊天", "tags": [], "localIcon": "图片/AI聊天/通义千问.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/通义千问.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI聊天/通义千问.png"}, {"id": "chat_006", "name": "豆包", "description": "豆包是字节跳动旗下基于云雀大模型开发的AI智能助手，主要用于智能对话问答和多场景辅助创作。它集聊天互动、写作、文案生成、翻译、编程辅助等多功能于一体，为用户提供信息获取、灵感激发和内容创作支持。豆包具有强大的知识获取能力，支持多模态处理和个性化对话，用户通过详细背景信息可获得定制解决方案。典型应用场景涵盖文案写作、短视频脚本生成、语言学习（如英语学习）、编程问题解答和日常聊天。豆包是字节跳动在AI领域进行产品化和场景验证的重要平台，虽然部分功能仍在优化中，但其作为高效便捷的内容创作及知识服务AI助手的定位已明确。", "url": "https://www.doubao.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/豆包.png", "categoryId": "AI聊天", "tags": [], "localIcon": "图片/AI聊天/豆包.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/豆包.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI聊天/豆包.png"}, {"id": "chat_007", "name": "腾讯元宝", "description": "腾讯元宝是腾讯公司基于其混元大模型于2024年5月底推出的AI工具平台。它由腾讯混元团队开发与技术支持，核心定位是提升用户在信息获取与内容创作方面的效率。主要功能包括强大的AI搜索（支持多源检索和解析）、AI阅读（信息概括与结构化）、AI创作（文本、图像、视频生成）以及多模态处理。腾讯元宝深度集成腾讯生态（微信、Q Q等），覆盖学习、办公、生活等多种场景的应用需求，例如文档处理、AI画图、指令定制等。其典型应用包括学习辅助、办公效率提升、内容创作和生活服务等，旨在通过AI技术全面升级工作与生活效率。", "url": "https://yuanbao.tencent.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/腾讯元宝.png", "categoryId": "AI聊天", "tags": [], "localIcon": "图片/AI聊天/腾讯元宝.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/腾讯元宝.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI聊天/腾讯元宝.png"}, {"id": "chat_008", "name": "<PERSON><PERSON>", "description": "Kimi AI是由北京初创公司Moonshot AI开发的多模态人工智能助手，于2025年1月发布Kimi 1.5版本。它以免费无限制使用为特色，具备强大的多模态处理能力，能理解并分析文本、图像和代码，支持实时网页搜索和多达50种文件格式的同时处理。Kimi在逻辑推理、复杂问题拆解等方面表现出色，尤其在编码辅助和API文档解读方面效率显著。其开发者是Moonshot AI。典型应用场景广泛，包括教育领域的学术资料整理和解题、商业领域的数据分析和报告生成，以及创意产业的创作辅助。Kimi AI代表了AI在多模态理解和应用上的前沿突破。", "url": "https://kimi.moonshot.cn", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/Kimi.png", "categoryId": "AI聊天", "tags": [], "localIcon": "图片/AI聊天/Kimi.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/Kimi.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI聊天/Kimi.png"}, {"id": "chat_009", "name": "Grok", "description": "Grok是由埃隆·马斯克创立的人工智能公司xAI开发的AI助手和聊天机器人，于2023年11月发布。Grok的核心优势在于能够通过网络及X（原Twitter）实时访问信息，拥有\"世界的实时知识\"，并被编程为以机智甚至\"叛逆\"的方式回答问题。它基于xAI的Grok 3等系列大模型，在推理、数学、编码等方面表现出色，支持DeepSearch（深度搜索和推理）、Think（思考过程展示）及Grok Vision（图像分析）等功能。开发者为xAI。典型应用包括实时信息获取、回答敏感问题、研究信息验证、编码辅助和创意生成等。", "url": "https://x.ai/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/Grok.png", "categoryId": "AI聊天", "tags": ["魔法"], "localIcon": "图片/AI聊天/Grok.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/Grok.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI聊天/Grok.png"}, {"id": "chat_010", "name": "<PERSON>", "description": "Poe是由Quora于2022年底推出的AI聊天机器人聚合平台。它本身不是AI模型，而是一个统一接口，允许用户在一个平台访问和使用来自不同公司（如OpenAI、Anthropic、Google等）的多个主流AI模型，如GPT-4、<PERSON>、Gemini等。Poe的主要功能是提供多模型访问、并排对话比较、自定义机器人构建（无需代码或通过Server Bots）以及图像生成功能（集成DALL·E 3、Ideogram等）。开发者信息指向Poe平台运营方Quora。典型应用场景广泛，包括比较不同模型输出、内容创作、问题解决、学习教育辅助和图像生成等，旨在简化AI模型的使用和管理。", "url": "https://www.poe.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/Poe.png", "categoryId": "AI聊天", "tags": ["魔法"], "localIcon": "图片/AI聊天/Poe.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/Poe.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI聊天/Poe.png"}, {"id": "chat_011", "name": "DeepAI", "description": "DeepAI是由Deep AI, Inc.开发的多功能人工智能平台。它提供丰富的AI工具，涵盖文本、图像、视频、音乐的生成与编辑。主要功能包括AI聊天、AI图像生成器（支持多种风格和高清模式）、背景移除、AI角色聊天、以及一系列图像处理API（如图像上色、超分辨率放大）。DeepAI旨在降低AI使用门槛，通过API支持开发者集成AI能力到自己的应用中，支持Python、JavaScript等多种语言。开发者为Deep AI, Inc.。典型应用场景包括数字内容创作（文本、图像、视频、音乐）、图像编辑与处理、娱乐互动（AI聊天与角色）以及开发者将AI功能集成到应用程序中。", "url": "https://deepai.org", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/DeepAI.png", "categoryId": "AI聊天", "tags": ["魔法"], "localIcon": "图片/AI聊天/DeepAI.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/DeepAI.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI聊天/DeepAI.png"}, {"id": "chat_012", "name": "<PERSON><PERSON><PERSON>", "description": "Mistral AI是一家法国AI初创公司，由前Meta和Google研究员于2023年创立，专注于开发高效、可扩展的开源和商用大型语言模型及多模态系统。其模型（如Mixtral 8x7b、Mistral Large）支持多编程语言和多语言处理，主要功能涵盖文本生成、图像分析、OCR、代码生成、功能调用等，并支持模型定制和部署。开发者是Arthur Mensch、Guillaume Lample、Timothée Lacroix等人创立的Mistral AI。典型应用场景包括企业级客服、内容创作自动化、跨语言翻译、本地部署AI助手及代码开发辅助等，尤其适用于对合规性和私有化部署有高要求的企业。", "url": "https://mistral.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/Mistral.png", "categoryId": "AI聊天", "tags": ["魔法"], "localIcon": "图片/AI聊天/Mistral.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/Mistral.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI聊天/Mistral.png"}, {"id": "chat_013", "name": "Meta.ai", "description": "Meta AI是Meta Platforms（原Facebook）旗下的人工智能研究部门和AI助手。它利用Meta的Llama模型构建，旨在提供个性化的AI体验。主要功能包括学习用户偏好以提供相关建议、新的语音交互方式（支持全双工对话）、跨平台（App、网页、智能眼镜）的无缝使用体验、集成图像生成和编辑功能，以及正在测试的文档处理能力。开发者为Meta。Meta AI的应用场景涵盖日常问题解答、提高工作与创造力效率（如高级图像创建、文档编辑），并计划扩展至客户服务和营销优化等领域。", "url": "https://www.meta.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/Meta.ai.png", "categoryId": "AI聊天", "tags": ["魔法"], "localIcon": "图片/AI聊天/Meta.ai.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/Meta.ai.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI聊天/Meta.ai.png"}, {"id": "chat_014", "name": "智谱清言", "description": "智谱清言是由智谱AI基于其自主研发的GLM大模型技术打造的智能对话和内容生成平台。其核心能力是多轮对话、通用问答、文本理解与生成，以及代码生成。主要功能涵盖内容创作（写作、作图）、职场提效（总结、报告）、学习辅助（知识问答、辅导）、编程辅助和长文档解析等。开发者为智谱AI团队。典型应用场景广泛，包括教育辅导、智能客服、行业信息归纳、创意内容生成和编程开发等，其开放的智能体API也支持开发者将其能力集成到第三方应用中，服务于多样化需求。", "url": "https://chatglm.cn/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/智谱清言.png", "categoryId": "AI聊天", "tags": [], "localIcon": "图片/AI聊天/智谱清言.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/智谱清言.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI聊天/智谱清言.png"}, {"id": "chat_015", "name": "讯飞星火", "description": "讯飞星火认知大模型是由科大讯飞推出的新一代认知智能大模型。它具备跨领域的知识和语言理解能力，支持自然对话交互，旨在实现从提出、规划到执行的任务闭环。主要功能非常全面，涵盖语言理解、知识问答（支持专属知识库）、逻辑推理、文本和代码生成、数学能力及强大的多模态能力（语音、图文、视频、数字人）。开发者为科大讯飞及其生态平台上的百万级开发者团队。典型应用场景广泛赋能千行百业，包括企业服务、智能硬件、政务、医疗、教育（讯飞AI学习机）、会议记录、办公效率工具、情感陪伴等，并提供丰富的API和定制服务。", "url": "https://xinghuo.xfyun.cn/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/讯飞星火.png", "categoryId": "AI聊天", "tags": [], "localIcon": "图片/AI聊天/讯飞星火.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/讯飞星火.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI聊天/讯飞星火.png"}, {"id": "chat_016", "name": "跃问", "description": "跃问（也称阶跃AI）是由阶跃星辰开发的中文多模态AI聊天机器人，由创始人姜大昕、首席科学家张祥雨等带领的团队开发。它基于阶跃星辰的Step系列模型，主打多模态交互，能够处理和理解文本、图像等多种信息，并支持自然的语言处理和多轮对话。主要功能包括多模态问答（识图、识文、语音）、长文本处理、信息检索与摘要、语言学习辅助及创意写作支持，并提供\"创意板\"功能供用户自定义应用。开发者为阶跃星辰。典型应用场景覆盖办公（文档解析）、学习（作业辅导、文献理解）和日常生活（问答、信息查询、图片识别）等多种需求。", "url": "https://yuewen.cn/ ", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/跃问.png", "categoryId": "AI聊天", "tags": [], "localIcon": "图片/AI聊天/跃问.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/跃问.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI聊天/跃问.png"}, {"id": "chat_017", "name": "文心一言", "description": "文心一言（ERNIE Bot）是由百度公司开发的生成式大语言模型和AI助理。其研发基础技术来自百度王海峰团队，并依靠百度整体AI技术实力及多个团队协同。文心一言的主要功能是作为一个智能助手和伴侣，提供聊天、问答、内容生成和修改、信息解释、摘要与提取等服务。它具备多模态能力，可生成文本、图像等内容。开发者为百度。典型应用场景包括智能问答、创意写作、代码编写、文档阅读与翻译、法律服务辅助、教育学习支持和营销文案生成等，通过提示词工程等技术提升AI性能，广泛应用于工作和生活场景。", "url": "https://yiyan.baidu.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/文心一言.png", "categoryId": "AI聊天", "tags": [], "localIcon": "图片/AI聊天/文心一言.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI聊天/文心一言.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI聊天/文心一言.png"}, {"id": "agent_001", "name": "<PERSON><PERSON>", "description": "Manus是一款专注于构建分布式多智能体协作系统的高性能开源框架，旨在解決大规模智能体协同决策的复杂难题。其核心在于提供认知控制中枢、多模态感知等功能，能有效拆解复杂任务并调用外部工具。Manus强调高性能、开源以及在实时性、资源分配和异构环境适应性上的优势，支持Python-first API和云边端协同。", "url": "https://manus.im/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI智能体/Manus.png", "categoryId": "AI智能体", "tags": [], "localIcon": "图片/AI智能体/Manus.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI智能体/Manus.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI智能体/Manus.png"}, {"id": "agent_002", "name": "<PERSON><PERSON>", "description": "Flowith是一个基于二维画布的AI生产力工具，通过其自研的Oracle智能体系统实现多线程、多模型协作。它擅长自主规划、拆解和执行多步骤复杂任务，并支持无限工具调用。其创新之处在于二维画布支持多分支内容管理，集成多种主流AI模型，并提供自动化构建动态知识库的知识花园功能。适用于复杂信息搜集与整理、多步骤任务完成（如市场调研、报告编写）、创意内容生成与对比以及团队协同创作等场景。", "url": "https://try.flowith.io/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI智能体/Flowith.png", "categoryId": "AI智能体", "tags": [], "localIcon": "图片/AI智能体/Flowith.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI智能体/Flowith.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI智能体/Flowith.png"}, {"id": "agent_003", "name": "<PERSON><PERSON><PERSON>", "description": "Lovart是一款专为视觉设计量身定制的AI智能体，核心功能是一站式自动化生成海报、品牌VI、Storyboard、广告等专业视觉内容，支持自然语言命令和精细化二次编辑。它集成多种AI模型并进行动态调度，拥有Talk-Tab-Tune高交互模式，提供完整设计工作流和PS级别图片编辑功能，甚至支持多模态（图片、视频、3D、BGM）生成。广泛应用于中小企业品牌视觉搭建、市场部高频创意创作、设计师辅助以及电商营销物料批量生成。", "url": "https://www.lovart.ai/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI智能体/Lovart.png", "categoryId": "AI智能体", "tags": [], "localIcon": "图片/AI智能体/Lovart.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI智能体/Lovart.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI智能体/Lovart.png"}, {"id": "agent_004", "name": "Genspark", "description": "Genspark主要通过多个AI Agent引擎提供深度、精准的个性化搜索结果，并专注于利用AI进行幻灯片（PPT）的生成与增强。其特点在于采用多AI Agent引擎针对特定信息类型进行多角度搜索，AI幻灯片功能支持自动生成大纲和视觉增强，并可调用超过40种工具。典型应用场景集中于AI幻灯片功能，适用于会议、演讲、教学和商业展示等场合，AI Agent搜索则用于高效深度信息获取。", "url": "https://www.genspark.ai/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI智能体/Genspark.png", "categoryId": "AI智能体", "tags": [], "localIcon": "图片/AI智能体/Genspark.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI智能体/Genspark.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI智能体/Genspark.png"}, {"id": "agent_005", "name": "扣子空间", "description": "扣子空间 (Coze) 是一个强大的通用AI Agent开发平台，旨在帮助用户低门槛快速搭建个性化和商业智能体。其核心功能包括灵活多节点的工作流编排、丰富的插件集成、记忆存储、多工具协作以及主导式互动，并支持一键发布至微信、豆包等多个平台。Coze以极低的开发门槛（零代码）和模块化组件为特色，能够实现复杂任务流自动化和多端无缝部署。广泛应用于信息收集助手、自动问答、内容生产、流程自动化和智能客服等多种场景。", "url": "https://www.coze.cn/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI智能体/扣子空间.png", "categoryId": "AI智能体", "tags": [], "localIcon": "图片/AI智能体/扣子空间.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI智能体/扣子空间.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI智能体/扣子空间.png"}, {"id": "search_001", "name": "perplexity", "description": "Perplexity AI是一款独特的AI问答引擎，它融合了对话式AI与实时网络搜索。其核心优势在于能够连接开放互联网，获取最新信息，并为答案提供可追溯的来源引用，增强了信息的可信度。适合需要实时、准确且注重信息来源验证的用户，尤其在研究、学习及日常知识查询方面展现出高效率。", "url": "https://www.perplexity.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI搜索/perplexity.png", "categoryId": "AI搜索", "tags": ["魔法"], "localIcon": "图片/AI搜索/perplexity.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI搜索/perplexity.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI搜索/perplexity.png"}, {"id": "search_002", "name": "夸克AI", "description": "夸克AI是一款定位为AI全能助手和AI超级应用的工具。它集成智能搜索、语音与图像识别、自动翻译及数据分析等多样化AI功能，提供极简、高效的一站式体验。夸克AI尤其受到年轻用户、学生和职场人士欢迎，帮助他们在学习、工作和日常生活中更轻松高效地处理信息和完成任务。", "url": "https://quark.cn/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI搜索/夸克AI.png", "categoryId": "AI搜索", "tags": [], "localIcon": "图片/AI搜索/夸克AI.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI搜索/夸克AI.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI搜索/夸克AI.png"}, {"id": "search_003", "name": "秘塔AI搜索", "description": "秘塔AI搜索的核心特色是其创新的'先思考框架，再搜索'模式，通过AI模型先构建逻辑框架，再进行大规模网页数据整合。该模式显著提升了信息检索速度与逻辑性，尤其适合效率优先和轻度研究需求的用户。秘塔强调无广告、直达结果的纯净体验，面向科研人员、管理咨询人员等专业知识工作者。", "url": "https://metaso.cn/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI搜索/秘塔AI搜索.png", "categoryId": "AI搜索", "tags": [], "localIcon": "图片/AI搜索/秘塔AI搜索.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI搜索/秘塔AI搜索.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI搜索/秘塔AI搜索.png"}, {"id": "search_004", "name": "纳米AI", "description": "纳米AI主打创新搜索与智能问答，支持文字、语音、拍照等多种输入方式及文档、音视频、链接分析。其亮点在于高访问量和广泛用户基础，'AI牛马'功能支持'0代码'搭建智能体。纳米AI尤其适合打工人/职场用户及泛大众，满足其办公自动化、内容分析和提升效率的需求。", "url": "https://bot.n.cn/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI搜索/纳米AI.png", "categoryId": "AI搜索", "tags": [], "localIcon": "图片/AI搜索/纳米AI.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI搜索/纳米AI.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI搜索/纳米AI.png"}, {"id": "search_005", "name": "问小白", "description": "问小白是由北京元石科技有限公司开发的人工智能助手，聚焦AI搜索、智能问答、文件解析、图片分析及推理画图等功能。它接入推理大模型（如DS-R1），强调\"更专业的AI搜索\"和\"秒速作答、精准快速\"。主要功能包括论文及研报自动生成、多格式文档与图片内容解析、推理画图文字转图像、数学推算及逻辑推理等。开发者为北京元石科技有限公司。典型应用场景广泛，覆盖学习辅导（拍题答疑）、职场效率提升（资料整理、写作辅助）、内容创作及行业分析等，适用于需高效处理信息和进行内容创作的学生、上班族及创作者。", "url": "https://www.wenxiaobai.com/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI搜索/问小白.png", "categoryId": "AI搜索", "tags": [], "localIcon": "图片/AI搜索/问小白.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI搜索/问小白.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI搜索/问小白.png"}, {"id": "img_001", "name": "SeaArt", "description": "SeaArt是一款基于Stable Diffusion的AI图像生成和编辑平台。其核心功能包括文生图、以图生图、智能画布、AI换脸以及独特的海象、魔术棒和控制网工具，提供精细的图像控制和修改能力。平台整合了超100万种模型，支持LoRA、ControlNet等，并以ComfyUI工作流为基础。主要面向游戏原画、插画、设计、电商、家居、美妆、虚拟网红等场景，以及社交媒体内容创作者。技术特点在于无需本地部署、快速生成高清图像、操作简单易用，并拥有活跃的创意社区。独特之处包括强大的功能与易用性结合、丰富的模型库和免费开放策略，以及优秀的用户体验和行业背景。用户群体广泛，尤其适合AI创作新手和对高画质、便捷操作有需求的用户。", "url": "https://www.seaart.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/SeaArt.png", "categoryId": "AI图像", "tags": [], "localIcon": "图片/AI图像/SeaArt.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/SeaArt.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI图像/SeaArt.png"}, {"id": "img_002", "name": "Civitai", "description": "Civitai是一个专注于AI模型（如LoRA, Checkpoint, ControlNet等）发现、分享和下载的社区平台。核心功能包括模型上传、分享、搜索，以及在线推理和强大的社区互动。主要用途涵盖AI艺术生成、游戏开发、设计插画、教育研究及商业应用，为AI内容创作者、模型开发者及消费型用户提供丰富的模型资源。技术上基于Stable Diffusion，强调开放创作生态和分布式网络，具备自主研发的语音识别和语义理解技术。独特之处在于其基于模型的开放社区生态和\"创作即挖矿\"机制，连接创作者、开发者和消费者，拥有多样风格的丰富模型资源和活跃的社区氛围，易于使用。", "url": "https://civitai.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Civitai.png", "categoryId": "AI图像", "tags": [], "localIcon": "图片/AI图像/Civitai.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Civitai.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI图像/Civitai.png"}, {"id": "img_003", "name": "Leonardo.Ai", "description": "Leonardo.Ai是一款强大的AI图像生成和编辑工具，支持通过文本提示生成图像，并提供多种预设模型及个性化模型训练功能。核心功能包括文本生成图像、以图生图、图像编辑工具、负面提示、后期编辑工具（如背景移除、画布编辑、图片放大）以及提示增强。主要应用于生成视觉资产、图像编辑优化、商业设计和创意项目，服务于艺术家、设计师、内容创作者、游戏开发者等创意专业人士。技术特点是基于先进的AI机器学习技术，提供多种预训练模型和用户自定义模型训练能力，界面友好且支持深度调整。独特之处在于高度可定制性、丰富的素材库、对细节的极致把控、生成高质量逼真图像的能力，以及超越同类平台的定制灵活性。", "url": "https://leonardo.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Leonardo.Ai.png", "categoryId": "AI图像", "tags": ["魔法"], "localIcon": "图片/AI图像/Leonardo.Ai.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Leonardo.Ai.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI图像/Leonardo.Ai.png"}, {"id": "img_004", "name": "PixAI.Art", "description": "PixAI.Art是一款高度聚焦动漫/二次元风格的AI艺术生成平台。核心功能是文本生成图像，支持风格迁移、图片处理（高清化、无损放大）、图片放大等工具，并提供丰富的定制化与模板，如角色定制和壁纸生成。主要用途在于动漫艺术创作、游戏角色/漫画场景生成、壁纸制作、商业设计以及图片后期处理。技术上基于先进AI深度学习算法，利用LoRA等微调模型实现个性化和风格多样性，擅长风格还原和细节表现力，支持多语言输入且生成速度快。目标用户主要是二次元爱好者、数字艺术创作者及设计师。独特优势在于其在二次元AIS领域的专注度、丰富的动漫风格模板、易用界面和活跃的社区互动，在动漫细节处理和分享互动方面表现突出。", "url": "https://pixai.art", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/PixAI.Art.png", "categoryId": "AI图像", "tags": ["魔法"], "localIcon": "图片/AI图像/PixAI.Art.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/PixAI.Art.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI图像/PixAI.Art.png"}, {"id": "img_005", "name": "Midjourney", "description": "Midjourney是一款领先的AI图像生成工具，以其生成高质量、高分辨率图像的能力而闻名。核心功能包括文本生成图像、高分辨率输出、风格与参数自定义、批量生成及快速渲染。特色功能包括\"外扩\"和优秀的对象识别能力，同时在图像细节表现和色彩还原方面处于行业前沿。主要用途广泛，涵盖平面设计、插画、视觉艺术、社交媒体内容、广告海报、游戏/影视概念设定、图书出版及写作辅助。技术上基于先进的AI算法，通过Discord命令进行互动，提供强大的自定义能力。目标用户包括设计师、市场营销人员、内容创作者及对高质量艺术性和社区互动有需求的用户。独特之处在于与Discord集成带来的社区协作便利、强大的自定义能力、丰富的输出风格以及行业领先的图像细节表现力。", "url": "https://www.midjourney.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Midjourney.png", "categoryId": "AI图像", "tags": ["魔法"], "localIcon": "图片/AI图像/Midjourney.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Midjourney.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI图像/Midjourney.png"}, {"id": "img_006", "name": "Getimg.ai", "description": "Getimg.ai是一款综合性AI视觉内容生成和编辑平台。核心功能包括多样化的文本到图像生成、图像到图像转换、AI视频生成以及强大的图像编辑工具（如修复、重绘、背景替换、放大）。平台支持模型训练和个性化风格创建，并提供批量管理功能。主要用途是快速生成各种视觉素材（插画、广告、社交媒体内容）、提升电商产品图和营销视觉质量、制作短视频内容，也服务于AI研究人员和广大普通用户。技术特点在于基于多种开放模型，支持多维度精细控制，整合图像、视频生成及编辑功能，提供灵活的图像管理系统。独特之处在于提供大量可用模型、允许付费训练用户自己的模型，以及一体化平台涵盖多种功能，操作简洁并支持自定义模型训练。", "url": "https://getimg.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Getimg.ai.png", "categoryId": "AI图像", "tags": ["魔法"], "localIcon": "图片/AI图像/Getimg.ai.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Getimg.ai.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI图像/Getimg.ai.png"}, {"id": "img_007", "name": "Ideogram", "description": "Ideogram是一款专注于高质量文本转图像生成的AI工具，尤其擅长精确和多样化的文本渲染。核心功能包括文本到图像生成、逼真图像生成、强大的文本渲染能力、支持多种图像风格标签、可调整的宽高比及分辨率、Magic Prompt功能（自动改进提示）以及图像上传和Remix功能。主要用途是通过文本提示高效创建各种视觉内容，如艺术作品、逼真照片和图表，快速启动创意项目，并适应不同平台和意图。技术上采用最新的AI模型，基于先进的机器学习模型，尤其擅长理解文本和图像关系以及复杂描述的映射，支持与主流设计工具集成。独特之处在于其在AI艺术生成领域文本渲染能力的突出表现，丰富的模板库和Magic Prompt功能，以及私有图像生成选项。", "url": "https://ideogram.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Ideogram.png", "categoryId": "AI图像", "tags": ["魔法"], "localIcon": "图片/AI图像/Ideogram.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Ideogram.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI图像/Ideogram.png"}, {"id": "img_008", "name": "Openart", "description": "Openart是一款功能丰富的AI艺术创作和编辑平台，提供多样化的AI工具集。核心功能包括文本生成图像、图像风格迁移、强大的图像编辑工具（修复人脸/手、背景移除、局部修补、放大）、批量生成、自定义模型训练、生成视频、一致性角色创作、草图转图像、QR码生成和AI滤镜。平台融合了100+模型和风格库，支持高分辨率放大和高度可定制的图像编辑。主要用途广泛应用于数字艺术创作、插画、平面设计、教育教学、小型企业数字营销、RPG/美术书创作以及内容生成辅助。独特之处在于\"无界限\"创作方式、可训练自定义模型、丰富强大的AI工具集、面向全龄全技能用户、浓厚的社区氛围以及一站式平台覆盖创作、编辑、定制和教育资源。", "url": "https://openart.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Openart.png", "categoryId": "AI图像", "tags": ["魔法"], "localIcon": "图片/AI图像/Openart.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Openart.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI图像/Openart.png"}, {"id": "img_009", "name": "即梦", "description": "即梦是字节跳动\"剪映\"旗下的一站式AI内容生成及编辑平台，特别强调中文友好性。核心功能涵盖AI图片创作（文生图、以图生图）、AI视频创作（文生视频、图生视频）及多样的AI图片编辑工具（背景替换、风格转换、人物姿势调整、无损放大等）。平台还提供智能画布功能，如拼图生成、局部重绘，以及AI数字人功能。主要用途是快速生成富有创意的图片、插画和视频素材，自动生成故事短视频，为设计师提供概念设计/原型制作辅助，以及社交媒体内容、影视广告的二次创作。技术特点是凭借字节跳动技术背景，中文语义理解能力强，对视频动效和连贯性处理表现出色，集成多种AI技术并提供一站式智能画布。独特优势在于中文友好、一站式平台、智能画布与社区生态、及其技术和产品生态支持。", "url": "https://dreamina.capcut.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/即梦.png", "categoryId": "AI图像", "tags": [], "localIcon": "图片/AI图像/即梦.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/即梦.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI图像/即梦.png"}, {"id": "img_010", "name": "<PERSON><PERSON>", "description": "Krea是一款集图像、视频、3D内容生成与编辑于一体的创新AI平台。核心功能包括Flux图像生成器、RealTime实时渲染、Enhancer图像增强（超高分辨率放大）、Edit编辑工具、Video Models视频工具（生成、风格转换、3D动画转换）以及Training训练功能。平台还支持3DKrea对象生成和先进的Lips Sync唇动同步技术。主要用途是创意领域（品牌设计、建筑可视化、影视、时尚）、快速生成定制视觉内容、故事讲述、宣传视频创作及教育学习。技术特点在于多模态生成、创新交互体验（\"ChatGPT Paint\"）、先进的唇动同步技术，并集成多种模型和ChatGPT。目标用户是创意专业人士、AI爱好者、初创公司及教育领域用户。独特之处是整合图像、视频、3D生成与编辑，结合开放训练模块和聊天式交互，支持超高分辨率输出和实时反馈，并具备强大的社区支持和多平台集成。", "url": "https://krea.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Krea.png", "categoryId": "AI图像", "tags": [], "localIcon": "图片/AI图像/Krea.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Krea.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI图像/Krea.png"}, {"id": "img_011", "name": "Shedevrum", "description": "Shedevrum是一款由Yandex开发的AI创意应用，提供文本到图像及多模态内容的生成能力。核心功能是文本到图像生成，支持生成文本、图像和短视频，并具备社区互动功能，允许用户查看、点赞和分享作品。主要用途包括艺术表达、故事讲述、教育目的、品牌推广及个性化内容创作，为不熟悉AI或复杂软件的用户提供便利，并可用于创建作品集和营销材料。技术特点是使用神经网络根据文本描述生成图像，由Yandex的YandexGPT神经网络驱动，利用先进AI算法实现准确细致的输出。目标用户广泛，涵盖艺术家、设计师、营销人员、教育工作者以及金融、医疗、零售等行业的企业。独特之处在于其易用性、创新性（通过文本生成视觉内容）、社区互动、多样化的风格，并在测试阶段免费提供，适用于广泛的应用场景。", "url": "https://shedevrum.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Shedevrum.png", "categoryId": "AI图像", "tags": ["魔法"], "localIcon": "图片/AI图像/Shedevrum.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Shedevrum.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI图像/Shedevrum.png"}, {"id": "img_012", "name": "p<PERSON><PERSON>n", "description": "piclumen是一款提供AI图像生成和编辑服务的平台，拥有多种微调模型和编辑工具。核心功能包括AI图像生成（文本描述）、AI图像编辑（AI替换、扩展、上采样、着色器、背景移除）、多种微调模型选择、集成第三方模型、高级参数调整、图像参考（角色、内容、风格、姿势控制）以及探索和Remix功能。主要用途是创建各种风格的AI艺术作品（概念艺术、角色设计、标志、时尚、产品、书籍插图等），并编辑和增强现有图像。技术特点是提供多种微调模型，支持图像参考和姿势控制，提供高级参数控制。目标用户包括艺术家、设计师、AI生成艺术爱好者以及寻求免费无水印AI图像生成工具的用户。独特之处在于提供免费无水印图像生成服务，集合多种微调模型和编辑工具，以及社区互动和灵感分享功能。", "url": "https://www.piclumen.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/piclumen.png", "categoryId": "AI图像", "tags": ["魔法"], "localIcon": "图片/AI图像/piclumen.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/piclumen.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI图像/piclumen.png"}, {"id": "img_013", "name": "Tensor.Art", "description": "Tensor.Art是一个集AI模型分享与在线图像生成于一体的平台。核心功能包括多样化输入（文本、草图、图像）和多风格图像生成，支持节点式生成工作流设计。平台集成先进的AI模型（Stable Diffusion, LoRA等），提供自定义参数调整，并支持模型上传下载和社区分享。主要用途是艺术创作实验、快速概念视觉化、专业视觉设计和创意内容生产，支持AI艺术从业者进行技术探索。平台托管SDXL等先进模型，提供在线生成和模型服务，实时运行AI推理引擎，并支持自定义模型训练接口。目标用户包括艺术创作者、设计师、初学者、AI开发者以及热爱尝试各种模型混合效果的用户。独特之处在于其综合模型分享与运行平台、节点式AI工具、多风格多输入支持、免费使用与社区互动，以及独占模型和托管SDXL的优秀效果。", "url": "https://tensor.art", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Tensor.Art.png", "categoryId": "AI图像", "tags": ["魔法"], "localIcon": "图片/AI图像/Tensor.Art.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Tensor.Art.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI图像/Tensor.Art.png"}, {"id": "img_014", "name": "liblib.art", "description": "liblib.art是一个面向中文用户的AI绘画模型下载与分享社区，同时提供网页端的在线作图和模型训练工具。核心功能包括海量AI绘画模型（超过10万种）的下载与分享，提供网页端原汁原味的webUI和comfyUI体验。平台整合了多种Stable Diffusion相关功能，如文本/图片生成、AI增强、人脸修复、姿态控制等，并支持在线训练Lora及用户上传原创模型。主要用途是服务于创作者的AI绘画实践、模型训练，并应用于艺术创作、插画、设计、二次元风格及个性化表情包等领域。技术特点是完全云端操作，集成度高，支持Lora训练和ComfyUI。目标用户覆盖AI美术初学者到进阶用户，尤其是没有本地高配计算资源但希望使用Stable Diffusion的用户，以及二次元和数字艺术爱好者、模型开发者。独特优势在于其作为中文本地化的原创模型资源平台，提供高度集成的在线工具，拥有强大的社区氛围和本地支付、注册体系。", "url": "https://www.liblib.art", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/liblib.art.png", "categoryId": "AI图像", "tags": [], "localIcon": "图片/AI图像/liblib.art.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/liblib.art.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI图像/liblib.art.png"}, {"id": "img_015", "name": "NightCafe Studio", "description": "NightCafe Studio是一个集AI艺术生成、多种模型集成和社区互动于一体的平台。核心功能包括文本到图像转换，集成了Stable Diffusion、FLUX.1、DALL-E 3等多种主流AI图像生成模型，并支持神经风格迁移和老方法。提供丰富的自定义和控制选项（如ControlNet、多风格、多提示、批量创建），以及图像增强功能。主要用途是创建、分享和讨论AI生成艺术，快速生成各类高质量艺术作品，应用于教育、社交媒体和个人表达。技术特点在于集成多种AI模型，提供用户友好界面，支持多样操作。目标用户是AI艺术爱好者、各种技能水平的艺术家、社区导向用户、营销人员及非专业人士。独特之处在于其强大的社区重点和功能，提供比其他平台更多样的AI模型选择，提供无限量免费Stable Diffusion生成，以及版权转移机制。", "url": "https://creator.nightcafe.studio", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/NightCafe_Studio.png", "categoryId": "AI图像", "tags": ["魔法"], "localIcon": "图片/AI图像/NightCafe_Studio.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/NightCafe_Studio.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI图像/NightCafe_Studio.png"}, {"id": "img_016", "name": "<PERSON><PERSON>", "description": "Recraft是一个专注于生成矢量图、插画和样机的AI设计平台。核心功能包括AI图像生成器、AI矢量生成器（生成LOGO、图标、品牌材料）、AI样机生成器、图像无损放大、强大的背景处理与智能擦除工具、以及风格库与风格一致性工具。平台采用设计语言驱动的AI模型（Recraft V3），支持灵活控制、高级自定义和全套AI图像编辑工具。主要用途是用于广告、电商、社交媒体内容、T恤设计、LOGO/图标制作、游戏角色设计、样机制作等场景，并可快速进行脑暴、模型设定及品牌视觉设计。技术特点在于其生成式AI设计工具，领先的AI模型，支持复杂文本生成，集成AI工具与手动编辑，并提供API接口。目标用户是专业设计师、创意工作者、小微企业主、电商品牌营销人及内容创作者。独特之处在于专注于矢量图生成、以设计语言驱动的AI模型、支持复杂文本输入、强大的后期编辑与控制能力，并提供端到端一站式AI智能设计解决方案和活跃的设计社区。", "url": "https://www.recraft.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Recraft.png", "categoryId": "AI图像", "tags": ["魔法"], "localIcon": "图片/AI图像/Recraft.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Recraft.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI图像/Recraft.png"}, {"id": "img_017", "name": "SnapEdit", "description": "SnapEdit是一款AI驱动的图片编辑工具，专注于移除照片中不需要的物体、人物、文字和瑕疵。核心功能包括智能移除对象、AI增强像素与修复老照片（一键提升画质，4K输出）、背景移除与编辑、AI皮肤修饰/美颜、电线/水印去除、AI增强（调整光线/色彩/构图）、背景虚化，以及生成式AI照片编辑，能重新构想图像并填充移除对象后的区域。主要用途是照片修复、电商商品图清洁、社交媒体内容优化、旅游/生活照片美化、移除干扰物、创建干净产品图和营销材料等。技术特点在于AI驱动的自动检测与删除功能，处理速度快且精准，保持图像质量，具备广泛的AI训练和生成式AI技术。目标用户包括个人用户、电商、内容创作者、市场营销人员、摄影师、设计师、小型企业、影响者、学生以及没有专业软件操作能力但有大量图片处理需求的用户。独特之处在于AI自动检测与一键删除的高效性、高画质处理、多种编辑工具集合、简易操作、以及生成式AI填充移除区域的能力。", "url": "https://snapedit.app", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/SnapEdit.png", "categoryId": "AI图像", "tags": ["魔法"], "localIcon": "图片/AI图像/SnapEdit.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/SnapEdit.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI图像/SnapEdit.png"}, {"id": "img_018", "name": "Kittl", "description": "Kittl是一款面向专业设计师和创意工作者的直观协作设计平台。核心功能包括直观的拖放设计工具、实时协作和共享、丰富的专业设计模板（T恤、海报、社交媒体、POD模板等）、高品质字体和高级文本效果，以及AI辅助功能（背景去除、图像生成、无损放大、智能擦除/补全、文本生成）。平台提供高保真模拟预览和无限画布，拥有大量矢量库。主要用途是创建专业级设计作品、营销内容制作、品牌建设（Logo设计、品牌资产）、按需印刷商品设计，并快速生成设计稿。技术特点在于AI工具与手动编辑结合，支持像素级到字体矢量调整，基于云端跨平台使用，并与Monotype合作提供高级字体库。目标用户包括专业设计师、创意工作者、独立设计师、营销团队、品牌方、印刷品电商及内容创作者。独特之处在于其直观协作设计平台特性，对字体编辑的深度专注，强调\"设计平台即生态\"，集成多种AI工具辅助设计，解决重复任务并激发创意。", "url": "https://www.kittl.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Kittl.png", "categoryId": "AI图像", "tags": ["魔法"], "localIcon": "图片/AI图像/Kittl.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI图像/Kittl.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI图像/Kittl.png"}, {"id": "design_001", "name": "自由画布", "description": "自由画布是由百度文库和百度网盘联合推出的AI万能白板，基于文心多模态大模型技术，为用户提供从内容输入、创作、编辑到分享的一站式解决方案。它打破了传统AI创作工具的局限，支持文档、PPT、PDF、图片、视频、音频等多种格式素材的自由拖拽输入，通过\"一拖一圈\"的极简操作实现全模态内容的混合理解与生成。核心功能包括多格式文件混合理解、智能内容生成、富媒体文档编辑、AI搜索及脑图生成等。平台能够对不同格式文件进行大意总结、框架参考等智能处理，支持生成包含图片、图表、视频、文本等多模态内容，并提供一键分享和云端存储功能。开发者为百度文库和百度网盘团队。典型应用场景包括教育教学（课件制作、学术报告）、办公商务（演示文稿、工作报告）、内容创作（多媒体创作、视频制作）、营销广告（素材设计、产品介绍）和个人娱乐（家庭相册、博客制作）等。其独特优势在于多模态素材融合、极简操作界面、智能内容理解与生成，以及与百度生态的深度整合。", "url": "https://wenku.baidu.com/board", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI设计/自由画布.png", "categoryId": "AI设计", "tags": [], "localIcon": "图片/AI设计/自由画布.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI设计/自由画布.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI设计/自由画布.png"}, {"id": "design_002", "name": "<PERSON><PERSON><PERSON>", "description": "Napkin AI是一款创新的文本转视觉化AI工具，由前Google员工Pramod Sharma和Jerome Scholler联合创立，专注于将文本内容快速转换为专业图表、信息图、流程图和图示。它利用先进的自然语言处理和生成式AI技术，能够智能理解文本内容并自动生成最相关的视觉呈现。核心功能包括AutoSpark一键生成示意图、智能文本分析、多样化图表选择（信息图、流程图、图表等）、丰富的自定义选项（颜色、字体、布局调整）和多格式导出（PNG、PDF、SVG）。平台支持英语、德语、法语、日语和中文等多种语言，与Google Slides、Microsoft Office、Notion、Medium等多个企业平台深度集成。开发者为前Google Docs团队成员创立的初创公司。典型应用场景涵盖演示文稿制作、博客文章配图、教育材料设计、商业报告视觉化、社交媒体内容创作和营销材料设计等。其独特优势在于极简的操作体验（无需复杂提示词）、高质量的视觉输出、强大的平台集成能力以及对中文内容的良好支持。", "url": "https://www.napkin.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI设计/Napkin.png", "categoryId": "AI设计", "tags": ["魔法"], "localIcon": "图片/AI设计/Napkin.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI设计/Napkin.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI设计/Napkin.png"}, {"id": "design_003", "name": "Whimsical", "description": "Whimsical是一款集成AI技术的协作设计平台，专注于提供思维导图、流程图、线框图和白板等多种视觉化工具。基于ChatGPT技术，它能够通过简单的文本提示快速生成结构化的思维导图和各类图表。核心功能包括AI驱动的思维导图生成、智能建议与扩展、多种视图模式（思维导图、流程图、线框图、白板）、实时团队协作、丰富的模板库和无限画布设计。平台提供直观的用户界面，支持一键生成、拖拽编辑和多人同时协作，并集成了强大的AI助手帮助用户打破思维障碍、激发创意灵感。开发者为Whimsical团队，专注于产品工作流程优化。典型应用场景包括产品设计（用户旅程图、产品原型）、项目管理（流程梳理、任务规划）、教育培训（知识结构图、课程设计）、头脑风暴（创意发散、团队讨论）和业务分析（流程优化、系统架构）等。其独特优势在于AI与传统设计工具的完美融合、强大的团队协作功能、多样化的视觉表达方式以及对敏捷开发和产品设计流程的深度适配。", "url": "https://whimsical.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI设计/Whimsical.png", "categoryId": "AI设计", "tags": ["魔法"], "localIcon": "图片/AI设计/Whimsical.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI设计/Whimsical.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI设计/Whimsical.png"}, {"id": "design_004", "name": "<PERSON><PERSON><PERSON>", "description": "AmyMind是一款轻量级的在线AI思维导图工具，以\"一句话生成思维导图\"为核心特色，无需注册即可使用。它基于先进的AI技术，能够从简短的文本输入、PDF、Markdown、Word等多种文件格式快速生成结构化思维导图。核心功能包括AI智能生成（文本转思维导图）、多格式文件导入、AI Branch智能扩展、AI Chat深度探索、富文本节点备注、大纲模式编辑、主题样式定制、节点图标标记和多格式导出（PPT、PDF、Word、Markdown、PNG等）。平台采用极简设计理念，提供直观的操作界面和强大的快捷键支持，数据可云端保存并支持一键分享。开发者为独立开发团队。典型应用场景包括学习复习（课堂笔记整理、知识点梳理）、项目管理（任务分解、流程规划）、会议记录（要点总结、讨论结构化）、创意构思（头脑风暴、想法组织）和教学辅助（课程结构、概念展示）等。其独特优势在于开箱即用的便捷性、强大的AI辅助能力、丰富的导出格式支持以及完全免费的基础功能，特别适合需要快速制作思维导图的个人用户和小团队。", "url": "https://amymind.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI设计/AmyMind.png", "categoryId": "AI设计", "tags": ["魔法"], "localIcon": "图片/AI设计/AmyMind.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI设计/AmyMind.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI设计/AmyMind.png"}, {"id": "design_005", "name": "稿定设计", "description": "稿定设计是国内领先的在线设计平台，集成了强大的AI智能设计引擎，为用户提供海报、Banner、社交媒体图片、视频等多种营销设计解决方案。平台拥有海量专业设计模板和丰富的AI工具矩阵，包括智能设计、AI绘图、AI抠图、AI文案生成、AI商品图制作等功能。核心特色是AI一键生成设计，用户只需输入需求或选择模板，AI即可自动匹配合适的设计风格、色彩搭配和布局方案。平台还提供强大的图片处理工具（AI抠图、背景移除、图片编辑）、视频制作工具、H5页面制作和创意画布等功能，支持多种尺寸自动适配和批量设计。开发者为稿定（厦门）科技有限公司，专注于降低设计门槛。典型应用场景涵盖电商营销（商品主图、促销海报）、社交媒体运营（小红书封面、公众号配图）、企业宣传（品牌物料、活动海报）、教育培训（课程海报、证书制作）和个人创作（朋友圈图片、节日贺卡）等。其独特优势在于丰富的中文模板库、强大的AI设计能力、一站式设计解决方案以及对国内各大平台规格的完美适配，特别适合中小企业和个人用户进行营销设计。", "url": "https://www.gaoding.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI设计/稿定设计.png", "categoryId": "AI设计", "tags": [], "localIcon": "图片/AI设计/稿定设计.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI设计/稿定设计.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI设计/稿定设计.png"}, {"id": "design_006", "name": "Tripo3D", "description": "Tripo3D是一款领先的AI驱动3D模型生成平台，专注于通过文本描述、单张图片、多视角图片甚至简单涂鸦快速生成高质量3D模型。基于先进的生成式3D基础模型技术，它能在10秒内创建具有详细几何结构和PBR材质的专业级3D模型。核心功能包括文本到3D模型转换、图像到3D模型生成、多视图图像处理、自动纹理贴图、多格式导出支持（GLB、FBX、OBJ、USD、STL等）和即将推出的3D场景生成、自动绑定和动画功能。平台支持实时预览和编辑，提供丰富的风格化选项和主题画廊，已生成超过600万个3D模型。开发者为Tripo团队，致力于降低3D内容创作门槛。典型应用场景包括游戏开发（角色和道具模型）、3D打印（原型设计、定制模型）、VR/AR应用（虚拟环境资产）、产品设计（概念验证、视觉展示）、建筑可视化（室内外装饰）和教育培训（3D教学模型）等。其独特优势在于极快的生成速度、专业级的模型质量、广泛的格式兼容性以及无需专业3D建模技能即可创作高质量3D内容，为元宇宙、游戏、AR/VR等领域提供强大的3D资产生产能力。", "url": "https://www.tripo3d.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI设计/Tripo3D.png", "categoryId": "AI设计", "tags": ["魔法"], "localIcon": "图片/AI设计/Tripo3D.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI设计/Tripo3D.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI设计/Tripo3D.png"}, {"id": "writing_001", "name": "Notion Al", "description": "Notion AI是由Notion推出的集成式AI写作助手，深度融合在Notion工作空间中，基于先进的大语言模型技术打造。它提供智能写作、内容生成、文本改写、语法检查、自动翻译、智能总结等全方位写作辅助功能。核心特色是与Notion工作空间的无缝集成，支持页面上下文感知，能根据已有内容生成相关建议。主要功能包括AI文档生成、会议记录总结、创意构思、格式调整、多语言翻译等。开发者为Notion团队。典型应用场景涵盖文档写作、会议记录整理、项目规划、知识管理、团队协作等，特别适合需要在统一平台进行写作和知识管理的个人用户、团队和企业，通过AI技术显著提升内容创作效率和质量。", "url": "https://www.notion.so", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI写作/Notion_Al.png", "categoryId": "AI写作", "tags": ["魔法"], "localIcon": "图片/AI写作/Notion_Al.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI写作/Notion_Al.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI写作/Notion_Al.png"}, {"id": "writing_002", "name": "笔灵", "description": "笔灵是由上海简办网络科技有限公司开发的专业AI写作工具，面向学生、职场人士和内容创作者，提供全方位的智能写作解决方案。平台深度学习20+种职位的200+工作场景真实案例，核心功能包括AI论文写作（千字大纲免费生成、万字论文生成）、AI PPT制作（答辩PPT+自述稿）、降AI痕迹检测、文章降重、AI小说创作等。特色功能是适配知网严苛检测的去AI痕迹技术，以及丰富的写作模板库。开发者为上海简办网络科技有限公司。典型应用场景包括毕业论文写作、课程作业、工作汇报、PPT制作、小说创作、新媒体内容生成等，累计服务超过200万用户，特别适合需要高质量学术写作和职场文档的学生群体和上班族。", "url": "https://ibiling.cn/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI写作/笔灵.png", "categoryId": "AI写作", "tags": [], "localIcon": "图片/AI写作/笔灵.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI写作/笔灵.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI写作/笔灵.png"}, {"id": "writing_003", "name": "新华妙笔", "description": "新华妙笔是由新华社媒体融合生产技术与系统国家重点实验室和博特智能联合研发的专业AI公文写作平台，专注于体制内公文写作场景。平台提供\"查、学、写、审\"一体化的人工智能公文写作与知识赋能协作服务，核心功能包括AI公文生成、智能校对、新华问道（权威思想学习）、公文范文库、素材库、模板库等。特色是基于习近平总书记思想、讲话等权威内容构建的智能问答学习平台，以及14个数据库的海量官方文章资源。开发者为新华社技术团队。典型应用场景涵盖党政机关公文写作、材料起草、政策解读、理论学习、党建工作等，特别适合政府机关、事业单位、国企央企等体制内工作人员，提供权威可靠的公文写作支持和政治理论学习服务。", "url": "https://miaobi.xinhuaskl.com/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI写作/新华妙笔.png", "categoryId": "AI写作", "tags": [], "localIcon": "图片/AI写作/新华妙笔.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI写作/新华妙笔.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI写作/新华妙笔.png"}, {"id": "writing_004", "name": "秘塔写作猫", "description": "秘塔写作猫是由上海秘塔网络科技有限公司开发的交互式中英文AI写作辅助平台，基于先进的人工智能技术提供全方位写作支持。核心功能包括智能纠错（支持语法、拼写、标点检查）、智能改写（多级别改写选项）、AI写作生成、自动总结、多语言翻译、语音输入等。平台特色是强大的文本校对能力和云同步功能，支持多种文档格式导入导出，提供版本控制和团队协作功能。开发者为上海秘塔网络科技有限公司。典型应用场景涵盖文档编辑、学术写作、商务文案、新闻稿件、博客创作、翻译工作等，提供基础版、高级版、团队版等多种服务选项，特别适合需要高质量文本编辑和校对的个人用户、企业团队和专业写作人员，通过AI技术显著提升写作效率和文本质量。", "url": "https://xiezuocat.com/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI写作/秘塔写作猫.png", "categoryId": "AI写作", "tags": [], "localIcon": "图片/AI写作/秘塔写作猫.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI写作/秘塔写作猫.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI写作/秘塔写作猫.png"}, {"id": "media_001", "name": "Hailuo AI", "description": "Hailuo AI 是由 MiniMax 开发的一款多功能 AI 工具，尤其专注于 AI 视频生成和多模态交互。其核心功能包括文生视频（Text-to-Video）、图生视频（Image-to-Video）以及基于主题的角色一致性视频生成。Hailuo AI 采用 MoE（专家混合）模型，旨在提升生成速度，特别适合需要快速响应和高质量输出的场景。除视频外，它也提供 AI 音乐创作功能，支持歌词生成和风格选择。其主要应用场景涵盖短视频创作、内容制作、广告营销等。Hailuo AI 的独特之处在于其 MoE 模型带来的生成速度优势以及在角色情感和表情的细微处理能力。", "url": "https://hailuoai.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Hailuo_AI.png", "categoryId": "AI视频", "tags": [], "localIcon": "图片/AI视频/Hailuo_AI.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Hailuo_AI.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI视频/Hailuo_AI.png"}, {"id": "media_002", "name": "<PERSON>ra", "description": "Sora 是由 OpenAI 构建的首个大型文本生成视频（Text-to-Video）模型。它能够根据详细的文本描述，生成长达 60 秒的视频，在视觉连贯性、复杂场景处理和物理世界模拟方面取得了显著突破。Sora 基于扩散模型和变换器架构，并借鉴了 DALL·E 3 的重述技术，能够生成包含复杂角色、特定动作和多镜头的逼真视频。其核心能力包括高质量文本到视频生成、复杂场景与角色模拟、多镜头生成、从静态图像或现有视频片段生成视频以及对物理世界的初步理解。Sora 的主要应用场景集中在内容创作、媒体产业、影视制作、营销广告、游戏设计和教育培训等领域。其独特之处在于将 AI 视频生成时长和质量提升到新高度，以及更深入地理解和模拟物理互动。", "url": "https://openai.com/sora", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Sora.png", "categoryId": "AI视频", "tags": ["魔法"], "localIcon": "图片/AI视频/Sora.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Sora.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI视频/Sora.png"}, {"id": "media_003", "name": "Loom", "description": "Loom 是一款领先的异步视频沟通与协作工具，主要功能包括屏幕录制、摄像头录像和音频捕捉，支持跨平台使用。用户可以快速录制视频并生成分享链接，实现即时传播。Loom 提供视频编辑、评论互动和云存储等功能，便于团队协作和信息传递。其显著特点是专注于\"异步视频\"概念，帮助用户通过视频替代传统邮件或会议，显著节省时间并提升沟通效率。Loom 还深度整合了 AI（Loom AI），提供自动生成标题、摘要、章节以及智能编辑等功能，进一步提升了工作流程效率。它广泛应用于企业内部沟通、产品演示、客户支持、销售、市场推广和教育培训等场景。Loom 的独特优势在于其极致简洁的操作体验、强大的 AI 赋能、异步沟通的理念普及以及广泛的用户基础和生态集成能力。", "url": "https://www.loom.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Loom.png", "categoryId": "AI视频", "tags": ["魔法"], "localIcon": "图片/AI视频/Loom.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Loom.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI视频/Loom.png"}, {"id": "media_004", "name": "klingai", "description": "KlingAI（可灵AI）是款由快手推出的 AI 创意生成平台，主打高质量图像与短视频的生成和编辑。核心功能包括通过文本或图像自动生成图片和视频，支持多种艺术风格和特效，最高可生成 1080P 分辨率的图像和长达 3 分钟的视频。KlingAI 具备生成复杂动作和顺序动作的能力，并提供视频延长和唇同步等特色编辑功能。平台支持定制化模型训练，用户可以创建个人专属角色模型。KlingAI 的主要应用场景涵盖社交媒体内容创作、艺术创作、广告营销以及普通用户的日常创意需求。其独特优势在于基于快手自研大模型实现技术领先，提供高质量输出和多样化定制选项，以及活跃的创作者社区。", "url": "https://klingai.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/klingai.png", "categoryId": "AI视频", "tags": ["魔法"], "localIcon": "图片/AI视频/klingai.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/klingai.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI视频/klingai.png"}, {"id": "media_005", "name": "PixVerse", "description": "PixVerse 是一款 AI 视频生成与图像处理工具，其独特之处在于强大的深度学习和计算机视觉技术基础。它的核心功能是将文本或静态图片转化为具有多样风格和动画效果的短视频，特别偏重于生成社交媒体风格的内容。PixVerse 提供丰富的定制化选项，允许用户调整视频的宽高比、色彩风格和动画速度等参数。除了视频生成，它还具备智能美颜、图像修复和创意设计（如艺术风格转换）等图像处理功能，集图像处理、识别与生成于一体。PixVerse 的主要应用场景包括短视频内容创作、图像后期处理、创意设计以及需要将静态元素转化为动态视频的营销和推广活动。其优势在于快速的 AI 视频生成能力、多样化的风格模板和用户友好的操作界面。", "url": "https://pixverse.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/PixVerse.png", "categoryId": "AI视频", "tags": ["魔法"], "localIcon": "图片/AI视频/PixVerse.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/PixVerse.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI视频/PixVerse.png"}, {"id": "media_006", "name": "Runwayml", "description": "Runwayml是一款领先的AI驱动视频生成与编辑平台，专注于影视制作、广告和创意内容生产。其核心技术平台，包括最新的Gen-4模型，能够实现从文本、图片生成高保真、具世界一致性的视频内容。平台提供丰富的AI工具，如文本转视频、图像转视频、物体移除、风格转换，并支持AI虚拟人生成及口型同步、音频合成等。Runwayml广泛应用于电影、广告、音乐视频等专业领域，帮助用户大幅提升创意效率、降低制作成本，并支持通过API进行大规模内容生产和集成。", "url": "https://runwayml.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Runwayml.png", "categoryId": "AI视频", "tags": ["魔法"], "localIcon": "图片/AI视频/Runwayml.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Runwayml.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI视频/Runwayml.png"}, {"id": "media_007", "name": "Vidnoz AI", "description": "Vidnoz AI是一款免费的AI视频生成与编辑平台，以其丰富的AI虚拟人和多语言语音合成能力为主要特色。平台提供超过1500个多风格AI虚拟人形象及1830多种AI声音，支持140多种语言，能够实现高精度语音克隆和口型同步。其核心功能包括文本转视频、AI视频编辑、视频翻译及本地化配音。Vidnoz AI适用于营销、教育、培训、电商、客户服务等多种商业场景，旨在帮助用户快速、高效地制作多语言、个性化的AI视频内容，有效提升沟通效率和转化率。", "url": "https://www.vidnoz.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Vidnoz_AI.png", "categoryId": "AI视频", "tags": ["魔法"], "localIcon": "图片/AI视频/Vidnoz_AI.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Vidnoz_AI.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI视频/Vidnoz_AI.png"}, {"id": "media_008", "name": "HeyGen", "description": "HeyGen是一款高效的AI视频及虚拟人生成工具，专注于通过文本输入快速创建带专业旁白的视频。平台提供多达500+虚拟人形象，并支持用户自定义数字孪生。HeyGen的核心优势在于其逼真的AI虚拟人和自然流畅的口型同步技术，结合AI语音克隆和支持175+语言的视频本地化功能。它广泛应用于营销、销售、企业培训、在线教育及社交媒体内容制作，帮助用户在无需摄像设备的条件下，在短时间内批量制作高质量的个性化视频，显著降低制作成本和时间。", "url": "https://www.heygen.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/HeyGen.png", "categoryId": "AI视频", "tags": ["魔法"], "localIcon": "图片/AI视频/HeyGen.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/HeyGen.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI视频/HeyGen.png"}, {"id": "media_009", "name": "Clipfly", "description": "Clipfly是一款集AI视频编辑和生成功能于一体的在线创作平台，无需专业技能即可使用。其核心功能包括文本转视频、图片转视频/动画、AI视频增强、AI物体移除以及AI虚拟人生成与语音合成。Clipfly提供多种有趣的AI特效，如AI Kissing、AI Dance等，并包含基础视频编辑工具。平台面向商业营销、社交媒体自媒体和个人娱乐等多种场景，以其低门槛、功能全面和智能化特色，帮助用户快速制作趣味性强、视觉效果出色的视频内容，尤其适用于短视频创作和个性化内容生成。", "url": "https://clipfly.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Clipfly.png", "categoryId": "AI视频", "tags": ["魔法"], "localIcon": "图片/AI视频/Clipfly.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Clipfly.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI视频/Clipfly.png"}, {"id": "media_010", "name": "Vivago", "description": "Vivago AI是一个综合性的AI视觉创作平台，专注于视频、图像及3D内容的生成与编辑。其主要功能包括文本转视频、图片转视频、文本转图片、图片转图片以及文本转3D模型。Vivago AI支持4K高清增强，并为社交媒体短视频优化了竖屏格式。平台提供Magic Brush、Magic Prompt、AI Replace等智能化工具，简化操作流程。Vivago AI适用于故事短片、科普讲解、营销广告、教育内容及3D设计等多元应用场景，其特色在于多媒体一体化创作、操作智能化和输出质量高，适合需要快速产出多样化高质量视觉内容的用户。", "url": "https://vivago.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Vivago.png", "categoryId": "AI视频", "tags": ["魔法"], "localIcon": "图片/AI视频/Vivago.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Vivago.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI视频/Vivago.png"}, {"id": "media_011", "name": "Viggle", "description": "Viggle AI 是一款免费的创新型AI视频生成工具，专注于将静态图像转化为物理真实的动态动画，尤其擅长角色动作和舞蹈视频创作。其强大的JST-1视频-3D基础模型能够驱动角色实现自然流畅的动作。主要功能包括文本到视频生成、动作混合与多角色替换、绿幕背景支持及背景移除、预设动作模板和团队协作功能。应用场景广泛，涵盖社交媒体短视频、营销推广、教育动画和娱乐视频制作。Viggle AI易于使用，无需专业技能即可快速创建高质量动态内容，特别适合内容创作者和营销人员。", "url": "https://viggle.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Viggle.png", "categoryId": "AI视频", "tags": ["魔法"], "localIcon": "图片/AI视频/Viggle.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Viggle.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI视频/Viggle.png"}, {"id": "media_012", "name": "Pollo", "description": "Pollo AI 是一款集成多种领先AI模型的综合性视频与图像生成平台，以\"低门槛、高自由度\"为特色，面向创作者、教育者和营销人员。它支持文本生成视频、图片动画、视频风格迁移和文本生成高清图片。Pollo AI提供40+视频滤镜和30+艺术风格，支持角色一致性、物理仿真特效、背景替换、视频增强等多种功能。内置模板和Prompt管理，支持批量生成。主要应用于社交媒体、市场营销、教育和个人创作。", "url": "https://pollo.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Pollo.png", "categoryId": "AI视频", "tags": ["魔法"], "localIcon": "图片/AI视频/Pollo.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Pollo.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI视频/Pollo.png"}, {"id": "media_013", "name": "Vidu", "description": "Vidu AI是一款将文本和图像转化为具一致性和流畅动画的AI视频生成工具。它的核心能力在于文本转视频和图像转视频功能，能够为静态图像赋予生命，并支持参考图像以确保角色或产品在视频中的一致性。Vidu AI提供提示语增强、视频升频及多种编辑选项，支持通用和动画风格选择，最高可生成16秒的1080p视频。主要应用于社交媒体内容、营销广告、教育视频、产品演示和故事板制作。目标用户为希望高效创建高质量视频的个人和团队。", "url": "https://vidu.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Vidu.png", "categoryId": "AI视频", "tags": ["魔法"], "localIcon": "图片/AI视频/Vidu.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Vidu.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI视频/Vidu.png"}, {"id": "media_014", "name": "Pika Art", "description": "Pika Art是由Pika Labs开发的生成式AI视频工具，旨在让任何人都能创作视频，无需专业经验。它通过文本或图像输入，在数秒内生成高质量、电影感的短视频片段。主要功能包括文本与图像生成视频，支持指定主体、场景、镜头运动和风格。内置多样编辑工具，如动态效果、镜头过渡、添加或替换元素。Pika Art的应用场景涵盖社交媒体内容、营销推广快速打样、教育讲解和实验性影像。目标用户是广大内容创作者、初学者以及专业团队。", "url": "https://pika.art", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Pika_Art.png", "categoryId": "AI视频", "tags": ["魔法"], "localIcon": "图片/AI视频/Pika_Art.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Pika_Art.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI视频/Pika_Art.png"}, {"id": "media_015", "name": "Luma AI", "description": "Luma AI专注于多模态生成式人工智能，主要应用于视频生成和3D建模创作。其核心产品Ray2和Dream Machine支持从文本、图片快速生成高质量自然运动短视频，具备精准镜头控制。Luma API赋能第三方开发者构建数字内容创新应用。应用领域涵盖创意、内容生产、游戏、媒体、广告、电影和教育等。目标用户为创意行业的专业人士和开发者。其技术亮点在于超高保真度和生成速度，以及复杂的物理和逻辑推理能力。", "url": "https://lumalabs.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Luma_AI.png", "categoryId": "AI视频", "tags": ["魔法"], "localIcon": "图片/AI视频/Luma_AI.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Luma_AI.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI视频/Luma_AI.png"}, {"id": "media_016", "name": "Synthesia", "description": "Synthesia AI 是一款领先的 AI 视频生成平台，专注于无需传统设备即可快速高效制作高质量视频。其核心优势在于支持超过 140 种语言和 230 多种逼真 AI 虚拟形象，提供强大的 AI 配音和声音克隆功能。平台内置 AI 驱动的视频编辑器和丰富的模板，便于非专业人士创作。Synthesia 广泛应用于企业培训、市场营销、教育和客户服务等领域，尤其适合需要大规模多语言视频内容的企业和团队，其合规保障和协作功能更增强了其企业级应用的价值。", "url": "https://www.synthesia.io", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Synthesia.png", "categoryId": "AI视频", "tags": ["魔法"], "localIcon": "图片/AI视频/Synthesia.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Synthesia.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI视频/Synthesia.png"}, {"id": "media_017", "name": "Fliki", "description": "Fliki AI 是一个将文本快速转化为视频内容的人工智能平台。它提供超过 2500 种逼真 AI 声音和 80 多种语言支持，用户可轻松将文本、博客或产品 URL 转换为带有 AI 旁白和虚拟形象的引人入胜的视频。Fliki 简化了视频制作流程，通过 AI 自动执行脚本生成、媒体选择和编辑等任务。其主要用途包括制作营销视频、教育内容、企业演示和社交媒体短视频。Fliki 面向内容创作者、营销人员、小型企业和教育工作者等广泛用户群体，旨在降低视频制作门槛，提高内容创建效率。", "url": "https://fliki.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Fliki.png", "categoryId": "AI视频", "tags": ["魔法"], "localIcon": "图片/AI视频/Fliki.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Fliki.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI视频/Fliki.png"}, {"id": "media_018", "name": "virbo", "description": "Virbo 是万兴公司推出的 AI 视频生成平台，通过逼真的人物形象、AI 配音和丰富的模板，帮助用户创建引人入胜的视频。平台提供 350 多种 AI 人物形象和 400 多种自然声音，支持 80 种语言及声音克隆。核心功能包括文本转视频、URL 转视频、AI 语音生成和视频翻译等。Virbo 广泛应用于商业营销、教育培训和内容创作等领域，通过简化制作流程和提供多语言能力，提升品牌形象并扩大受众范围。它适合营销人员、教育者和企业等多种用户，强调高效和本地化内容制作。", "url": "https://virbo.wondershare.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/virbo.png", "categoryId": "AI视频", "tags": [], "localIcon": "图片/AI视频/virbo.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/virbo.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI视频/virbo.png"}, {"id": "media_019", "name": "Creatify", "description": "Creatify AI 是一款专注于帮助企业和营销人员制作视频广告的 AI 工具，并提供数字广告情报服务。核心功能包括 AI 头像库（超过 500 个）、文本到语音转换（支持 140+ 声音角色、29 种语言）和 AI 脚本编写。Creatify 可快速将 URL、脚本或视觉资产转换为专业的视频广告，并提供编辑工具。其独特之处在于支持跟踪和分析竞争对手的广告。Creatify 主要面向需要快速、经济高效地制作高质量广告的中小型企业、初创公司和营销人员，是一款生成视频广告的有力工具。", "url": "https://www.creatify.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Creatify.png", "categoryId": "AI视频", "tags": ["魔法"], "localIcon": "图片/AI视频/Creatify.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI视频/Creatify.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI视频/Creatify.png"}, {"id": "audio_001", "name": "<PERSON><PERSON>", "description": "Suno是一款强大的AI音乐创作工具，用户只需文本描述即可生成包含歌词和人声的完整原创歌曲。基于自研Chirp模型，支持多样音乐风格、多语种，输出广播级音质，并含AI水印。面向所有人，尤其内容创作者及专业音乐人，付费版可商用。降低创作门槛，高效实现个性化音乐。", "url": "https://www.suno.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI音频/Suno.png", "categoryId": "AI音频", "tags": ["魔法"], "localIcon": "图片/AI音频/Suno.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI音频/Suno.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI音频/Suno.png"}, {"id": "audio_002", "name": "Riffusion", "description": "Riffusion是一款基于AI的音乐生成工具，通过文本提示快速创作独特原创音乐。利用Stable Diffusion将文本转为频谱图再生成音频，支持多种流派、乐器和风格定制，实时生成。目前免费使用，主要服务于内容创作者、音乐人、教育者和爱好者，提供即兴创作灵感。", "url": "https://www.riffusion.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI音频/Riffusion.png", "categoryId": "AI音频", "tags": ["魔法"], "localIcon": "图片/AI音频/Riffusion.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI音频/Riffusion.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI音频/Riffusion.png"}, {"id": "audio_003", "name": "Voicemod", "description": "Voicemod是实时AI语音变声及音效板软件，支持Windows/macOS。提供超200种变声音效及AI自定义语音功能，用于游戏、直播、内容创作、在线会议等场景。操作简便，低延迟，兼容性强，帮助用户塑造在线声音身份，提升娱乐与互动体验。", "url": "https://www.voicemod.net", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI音频/Voicemod.png", "categoryId": "AI音频", "tags": ["魔法"], "localIcon": "图片/AI音频/Voicemod.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI音频/Voicemod.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI音频/Voicemod.png"}, {"id": "audio_004", "name": "Udio", "description": "Udio是一款将文本提示转化为高质量音乐的AI平台。用户可输入歌词或描述，生成包含人声的原创歌曲，支持自定义流派、人声、乐器及编辑。提供免费额度，面向音乐家、词曲作者、内容创作者及爱好者。旨在降低音乐制作难度，简化流程，激发创意。", "url": "https://udio.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI音频/Udio.png", "categoryId": "AI音频", "tags": ["魔法"], "localIcon": "图片/AI音频/Udio.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI音频/Udio.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI音频/Udio.png"}, {"id": "audio_005", "name": "<PERSON><PERSON><PERSON>", "description": "Mureka AI是端到端的AI音乐创作与盈利平台。用户可基于风格参考、人声、旋律或文本快速生成原创音乐，支持歌词生成及细致编辑。提供内置市场用于音乐销售和授权，并支持分发。面向专业/业余音乐人、内容创作者，致力于实现AI音乐创作的变现与发行。", "url": "https://mureka.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI音频/Mureka.png", "categoryId": "AI音频", "tags": ["魔法"], "localIcon": "图片/AI音频/Mureka.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI音频/Mureka.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI音频/Mureka.png"}, {"id": "audio_006", "name": "moisesa", "description": "Moises App是AI驱动的音乐工具，核心功能为音轨分离（人声、鼓等）。支持智能节拍器、速度/音高调整、和弦检测、AI母带等。广泛用于乐手、歌手、制作人、教师和学生进行练习、学习、创作伴奏及分析歌曲。界面友好，功能全面，深受全球音乐人欢迎。", "url": "https://moises.ai/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI音频/moisesa.png", "categoryId": "AI音频", "tags": ["魔法"], "localIcon": "图片/AI音频/moisesa.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI音频/moisesa.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI音频/moisesa.png"}, {"id": "audio_007", "name": "<PERSON><PERSON>", "description": "LANDR是集AI母带处理、数字音乐分发、素材库及协作于一体的综合平台。其AI母带引擎快速高效，助作品达到商业标准。支持一键分发至全球主流平台，提供营销工具。面向独立音乐人、制作人、厂牌及内容创作者，提供从创作到发行、推广的全流程解决方案。", "url": "https://www.landr.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI音频/Landr.png", "categoryId": "AI音频", "tags": ["魔法"], "localIcon": "图片/AI音频/Landr.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI音频/Landr.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI音频/Landr.png"}, {"id": "audio_008", "name": "Brain.fm", "description": "Brain.fm是功能性音乐应用，利用专利技术通过特定节奏引导大脑活动，旨在增强专注、放松、睡眠和冥想能力。提供多种模式，支持定制，有科学研究支持其有效性。适用于需要提高效率、改善睡眠或有注意力障碍的人群，帮助优化认知状态。", "url": "https://www.brain.fm", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI音频/Brain.fm.png", "categoryId": "AI音频", "tags": ["魔法"], "localIcon": "图片/AI音频/Brain.fm.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI音频/Brain.fm.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI音频/Brain.fm.png"}, {"id": "audio_009", "name": "Soundraw", "description": "Soundraw是面向内容创作者的AI音乐生成工具，快速创建免版税原创音乐。用户可按心情、流派、长度等定制，支持流派混合与结构编辑。所有音乐版权安全，可商用。通过API服务企业。适合视频博主、播客主、游戏开发者及企业，高效获取定制化背景音乐。", "url": "https://soundraw.io", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI音频/Soundraw.png", "categoryId": "AI音频", "tags": ["魔法"], "localIcon": "图片/AI音频/Soundraw.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI音频/Soundraw.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI音频/Soundraw.png"}, {"id": "audio_010", "name": "Samplette", "description": "Samplette是音乐片段发现平台，专注于帮助制作人、DJ及采样爱好者寻找和管理可采样音乐片段。提供强大搜索、筛选（按年份、曲风、观看量等）及随机发现功能，集成播放列表。通过与第三方合作简化采样清除。是缺乏线下资源的数字音乐创作者获取灵感的便捷工具。", "url": "https://www.samplette.io", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI音频/Samplette.png", "categoryId": "AI音频", "tags": ["魔法"], "localIcon": "图片/AI音频/Samplette.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI音频/Samplette.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI音频/Samplette.png"}, {"id": "office_001", "name": "Gamma", "description": "Gamma AI是AI驱动的设计工具，用于快速创建演示文稿、文档和网页。用户输入想法或大纲，AI即可生成内容、布局、媒体和图像。支持多种模板、品牌定制及实时协作，可导出为PPT/PDF/网页等。通过AI大幅提升内容创建效率，专注于*想法*而非设计细节。", "url": "https://gamma.app", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI办公/Gamma.png", "categoryId": "AI办公", "tags": ["魔法"], "localIcon": "图片/AI办公/Gamma.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI办公/Gamma.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI办公/Gamma.png"}, {"id": "office_002", "name": "通义效率", "description": "通义效率是阿里巴巴\"通义\"AI品牌下的一系列办公效率工具集合，基于大模型驱动。核心功能包括智能会议记录、音视频内容理解与分析（如通义听悟）、通用AI助手及内容生成等。旨在通过AI技术帮助用户记录、整理和分析信息，大幅提升日常工作效率，特别是会议和音视频处理方面。", "url": "https://tongyi.aliyun.com/efficiency", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI办公/通义效率.png", "categoryId": "AI办公", "tags": [], "localIcon": "图片/AI办公/通义效率.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI办公/通义效率.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI办公/通义效率.png"}, {"id": "office_003", "name": "飞书多维表格", "description": "飞书多维表格（Lark Base）是强大的在线数据管理与协作工具，结合表格与数据库特性。核心功能包括多维数据管理、看板/甘特图/表单等多种视图切换、在线协作、自动化及AI赋能（如AI捷径字段）。广泛应用于项目管理、客户管理等，助团队高效管理数据、优化流程。", "url": "https://sheet.feishu.cn/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI办公/飞书多维表格.png", "categoryId": "AI办公", "tags": [], "localIcon": "图片/AI办公/飞书多维表格.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI办公/飞书多维表格.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI办公/飞书多维表格.png"}, {"id": "office_004", "name": "NotebookLM", "description": "Google NotebookLM是一款基于Gemini大模型的AI虚拟研究助手。它能汇聚多种来源资料（文档、网页、音视频等），自动总结、提炼要点、深度问答，并提供出处引用。支持生成演示大纲、创意洞察及音频总览，助用户高效学习、研究和内容创作。数据安全可靠。", "url": "https://notebooklm.google.com/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI办公/NotebookLM.png", "categoryId": "AI办公", "tags": ["魔法"], "localIcon": "图片/AI办公/NotebookLM.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI办公/NotebookLM.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI办公/NotebookLM.png"}, {"id": "office_005", "name": "通义听悟", "description": "通义听悟是阿里云出品的音视频AI助手，专注内容理解与转写。核心功能包括实时语音转文字、说话人区分、智能摘要、要点提炼及多语言翻译。支持播客等多种源文件处理，实现\"一小时内容五分钟转写\"的高效。助用户快速整理和分析会议、访谈等音视频信息，提升工作学习效率。", "url": "https://tingwu.aliyun.com/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI办公/通义听悟.png", "categoryId": "AI办公", "tags": [], "localIcon": "图片/AI办公/通义听悟.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI办公/通义听悟.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI办公/通义听悟.png"}, {"id": "office_006", "name": "SmallPDF", "description": "Smallpdf是功能全面的在线PDF处理工具，提供21+种工具。核心功能涵盖PDF与Word/Excel/PPT/JPG等格式互转（含OCR）、高效压缩（最高99%）、编辑、合并、分割、签署等。近期新增AI PDF工具。安全易用，支持跨平台访问，是处理PDF文档的便捷一站式方案。", "url": "https://smallpdf.com/cn", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI办公/SmallPDF.png", "categoryId": "AI办公", "tags": [], "localIcon": "图片/AI办公/SmallPDF.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI办公/SmallPDF.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI办公/SmallPDF.png"}, {"id": "office_007", "name": "PDFCandy", "description": "PDFCandy是专注于在线PDF处理的工具，尤其擅长格式转换，支持Word、Excel、PPT及HTML、ePUB等多种冷门格式与PDF互转。此外提供PDF合并、压缩、编辑、加密等基础功能。虽然智能编辑能力相对基础，但其细致全面的格式转换是突出特点，免费且易于使用。", "url": "https://pdfcandy.com/cn/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI办公/PDFCandy.png", "categoryId": "AI办公", "tags": [], "localIcon": "图片/AI办公/PDFCandy.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI办公/PDFCandy.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI办公/PDFCandy.png"}, {"id": "office_008", "name": "Kimi PPT助手", "description": "Kimi PPT助手是月之暗面Kimi Chat关联的AI工具，旨在通过AI技术快速生成高质量演示文稿。用户可通过自然语言或文档生成大纲及完整PPT，支持在线编辑、模板选择、多种格式下载分享。功能全面、操作简便，大幅提升PPT制作效率，是快速完成商业汇报、学术演讲等演示文稿的利器。", "url": "https://kimi.moonshot.cn/ppt", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI办公/Kimi_PPT助手.png", "categoryId": "AI办公", "tags": [], "localIcon": "图片/AI办公/Kimi_PPT助手.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI办公/Kimi_PPT助手.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI办公/Kimi_PPT助手.png"}, {"id": "coding_001", "name": "<PERSON><PERSON><PERSON>", "description": "Cursor是基于Visual Studio Code构建的AI-first代码编辑器，由Anysphere团队开发，专注于通过人工智能技术revolutionize编程体验。它集成了强大的AI编程助手，支持自然语言编程、智能代码补全、代码解释和重构等功能。核心特色包括Composer（AI代码生成器）、Tab（智能补全）、Chat（代码对话）、Cmd+K（快速编辑）等功能，能够理解整个代码库上下文并提供精准建议。Cursor支持多种编程语言，包括Python、JavaScript、TypeScript、React、Go等，并提供强大的调试和重构能力。开发者为Anysphere团队，致力于让AI成为程序员的得力助手。典型应用场景包括全栈开发、前端React/Vue项目开发、后端API开发、数据科学项目、开源项目维护等，特别适合需要快速开发和代码质量要求较高的个人开发者、初创团队和企业开发团队，通过AI显著提升编程效率和代码质量。", "url": "https://cursor.sh", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/Cursor.png", "categoryId": "AI编程开发", "tags": ["魔法"], "localIcon": "图片/AI编程开发/Cursor.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/Cursor.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI编程开发/Cursor.png"}, {"id": "coding_002", "name": "GitHub Copilot", "description": "GitHub Copilot是由GitHub与OpenAI联合开发的革命性AI编程助手，基于OpenAI Codex模型，通过数十亿行开源代码训练而成。它能够在编辑器中实时提供智能代码建议和补全，支持数十种编程语言包括Python、JavaScript、TypeScript、Ruby、Go、C#、C++等。核心功能包括行级和函数级代码补全、注释转代码、代码解释、单元测试生成、代码优化建议等。Copilot深度集成到VS Code、Visual Studio、Neovim、JetBrains IDEs等主流开发环境中，提供无缝的开发体验。开发者为GitHub和OpenAI团队。典型应用场景涵盖Web开发、移动应用开发、数据科学、机器学习、DevOps自动化、开源项目贡献等，特别适合各个水平的程序员，从初学者学习编程到资深开发者提升效率，已成为全球数百万开发者的必备工具，显著提升编程生产力和代码质量。", "url": "https://github.com/features/copilot", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/GitHub_Copilot.png", "categoryId": "AI编程开发", "tags": ["魔法"], "localIcon": "图片/AI编程开发/GitHub_Copilot.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/GitHub_Copilot.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI编程开发/GitHub_Copilot.png"}, {"id": "coding_003", "name": "v0.dev", "description": "v0.dev是由Vercel开发的革命性AI UI生成器，专注于通过自然语言描述快速生成现代化的React组件和界面。基于先进的大语言模型，它能够理解用户的设计需求并自动生成符合现代设计规范的UI代码，支持Tailwind CSS、shadcn/ui组件库等流行技术栈。核心功能包括文本转UI代码、实时预览、代码编辑、组件库集成、响应式设计自动适配等。v0.dev生成的代码质量高、结构清晰，支持TypeScript，并遵循最佳实践。平台提供丰富的预设模板和样式选项，支持深度自定义和快速迭代。开发者为Vercel团队，依托其在前端技术领域的深厚积累。典型应用场景包括快速原型设计、组件库开发、着陆页制作、管理后台界面、电商页面设计、博客主题开发等，特别适合前端开发者、UI/UX设计师、产品经理和需要快速构建现代化Web界面的创业团队，大幅缩短从设计到代码的转换时间。", "url": "https://v0.dev", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/v0.dev.png", "categoryId": "AI编程开发", "tags": ["魔法"], "localIcon": "图片/AI编程开发/v0.dev.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/v0.dev.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI编程开发/v0.dev.png"}, {"id": "coding_004", "name": "Framer AI", "description": "Framer AI是Framer设计平台集成的人工智能功能，旨在革新数字产品设计和开发流程。基于先进的AI技术，它能够通过自然语言描述自动生成高保真的交互式设计稿和可发布的网站代码。核心功能包括AI网站生成、智能组件创建、自动布局优化、响应式设计适配、交互动画生成、内容智能填充等。Framer AI支持从文本描述到完整网站的一键生成，生成的设计遵循现代设计原则，包含精美的视觉效果和流畅的交互体验。平台提供丰富的模板库、组件系统和自定义选项，支持团队协作和版本控制。开发者为Framer团队，专注于lowering设计和开发的门槛。典型应用场景包括快速网站原型制作、产品着陆页设计、品牌官网开发、设计系统构建、营销页面创建、个人作品集网站等，特别适合设计师、产品经理、创业者和小团队，无需编程技能即可创建专业级的交互式网站和数字产品。", "url": "https://www.framer.com/ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/Framer_AI.png", "categoryId": "AI编程开发", "tags": ["魔法"], "localIcon": "图片/AI编程开发/Framer_AI.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/Framer_AI.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI编程开发/Framer_AI.png"}, {"id": "coding_005", "name": "Codeium", "description": "Codeium是一款完全免费的AI代码补全和编程助手工具，由Codeium团队开发，致力于为全球开发者提供高质量的AI编程支持。基于先进的大语言模型训练，它支持70+种编程语言，包括Python、JavaScript、TypeScript、Java、C++、Go、Rust等主流语言。核心功能包括智能代码补全、多行代码生成、代码解释、bug修复建议、代码重构、注释生成、单元测试编写等。Codeium深度集成到40+种主流IDE和编辑器中，如VS Code、IntelliJ IDEA、PyCharm、Sublime Text、Vim等，提供无缝的开发体验。平台特色是完全免费且功能强大，无使用限制，支持本地部署以保护代码隐私。开发者为Codeium Inc.团队。典型应用场景涵盖全栈Web开发、移动应用开发、系统编程、数据科学、机器学习、开源项目贡献等，特别适合个人开发者、学生、小团队和对成本敏感的企业，提供企业级的AI编程体验而无需付费，是GitHub Copilot的优秀免费替代方案。", "url": "https://codeium.com/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/Codeium.png", "categoryId": "AI编程开发", "tags": ["魔法"], "localIcon": "图片/AI编程开发/Codeium.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/Codeium.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI编程开发/Codeium.png"}, {"id": "coding_006", "name": "trae.ai", "description": "trae.ai是一款创新的AI辅助开发平台，专注于通过人工智能技术简化和加速软件开发流程。基于先进的机器学习模型，它提供智能代码生成、项目架构建议、自动化测试、代码审查等全方位开发支持。核心功能包括AI驱动的代码补全、智能bug检测与修复、性能优化建议、技术栈选择指导、项目结构生成等。平台支持多种编程语言和框架，能够理解项目上下文并提供个性化建议。trae.ai特别注重开发效率的提升，通过AI减少重复性工作，让开发者专注于核心业务逻辑。平台提供直观的用户界面和强大的集成能力，支持与主流开发工具的无缝对接。开发者为trae.ai团队，致力于推动AI在软件开发领域的应用。典型应用场景包括快速应用原型开发、代码质量改进、技术债务分析、新项目架构设计、团队代码标准化等，特别适合中小型开发团队、初创公司和希望提升开发效率的企业，通过AI技术显著减少开发时间和提高代码质量。", "url": "https://trae.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/trae.ai.png", "categoryId": "AI编程开发", "tags": ["魔法"], "localIcon": "图片/AI编程开发/trae.ai.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/trae.ai.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI编程开发/trae.ai.png"}, {"id": "coding_007", "name": "FlutterFlow AI Gen", "description": "FlutterFlow AI Gen是FlutterFlow低代码开发平台集成的人工智能生成功能，专门为Flutter移动应用开发而设计。基于先进的AI技术，它能够通过自然语言描述自动生成Flutter应用的UI界面、数据模型、业务逻辑和完整的应用原型。核心功能包括AI应用生成、智能UI设计、数据库模式创建、API集成建议、状态管理优化、响应式布局适配等。平台支持从简单的文本描述到复杂应用的端到端生成，生成的代码遵循Flutter最佳实践，包含Material Design或Cupertino设计规范。FlutterFlow AI Gen与Google的Firebase、Supabase等后端服务深度集成，支持实时数据同步、用户认证、云存储等功能。开发者为FlutterFlow团队，依托Google Flutter框架的强大生态。典型应用场景包括快速移动应用原型制作、MVP开发、企业内部工具、电商应用、社交媒体应用、教育应用等，特别适合产品经理、设计师、初级开发者和需要快速交付移动应用的创业团队，无需深入的Flutter编程知识即可创建功能完整的跨平台移动应用。", "url": "https://flutterflow.io/ai-gen", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/FlutterFlow_AI_Gen.png", "categoryId": "AI编程开发", "tags": [], "localIcon": "图片/AI编程开发/FlutterFlow_AI_Gen.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/FlutterFlow_AI_Gen.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI编程开发/FlutterFlow_AI_Gen.png"}, {"id": "coding_008", "name": "Warp AI", "description": "Warp AI是现代化终端Warp内置的人工智能助手，旨在革新开发者的命令行体验。基于先进的大语言模型，它能够理解自然语言并转换为精确的shell命令，提供智能命令建议、错误诊断、性能优化等功能。核心功能包括自然语言转命令行、智能命令补全、历史命令搜索、错误解释与修复建议、工作流程自动化、团队协作功能等。Warp AI支持多种shell环境（bash、zsh、fish等）和操作系统（macOS、Linux），集成了丰富的开发工具生态。平台特色是将现代化的用户界面与传统终端的强大功能相结合，提供可视化的命令历史、智能搜索、块状输出等创新特性。开发者为Warp团队，专注于重新定义终端体验。典型应用场景包括系统管理、DevOps操作、Git版本控制、Docker容器管理、云服务部署、数据库操作、文件系统管理等，特别适合后端开发者、运维工程师、数据工程师和所有需要频繁使用命令行的技术人员，通过AI显著降低命令行学习曲线并提升操作效率。", "url": "https://www.warp.dev/", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/Warp_AI.png", "categoryId": "AI编程开发", "tags": ["魔法"], "localIcon": "图片/AI编程开发/Warp_AI.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/Warp_AI.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI编程开发/Warp_AI.png"}, {"id": "coding_009", "name": "Windsurf", "description": "Windsurf是由Codeium团队开发的新一代AI-first集成开发环境，专注于通过先进的人工智能技术革新代码编写和调试体验。基于强大的AI模型，它提供智能代码生成、实时错误检测、自动化调试、代码优化建议等全方位编程支持。核心功能包括AI驱动的代码补全、智能重构建议、自动化测试生成、实时代码分析、多语言支持等。Windsurf特别注重开发工作流的优化，通过AI助手显著减少调试时间，提升代码质量和开发效率。平台支持多种主流编程语言和框架，提供直观的用户界面和强大的项目管理功能，集成了版本控制、团队协作等企业级功能。开发者为Codeium团队，致力于打造下一代智能开发环境。典型应用场景包括全栈Web开发、移动应用开发、企业级软件开发、开源项目维护、代码审查和重构等，特别适合需要高效开发和代码质量保障的个人开发者、技术团队和企业，通过AI技术显著提升编程生产力并降低开发门槛。", "url": "https://www.windsurf.ai", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/Windsurf.png", "categoryId": "AI编程开发", "tags": ["魔法"], "localIcon": "图片/AI编程开发/Windsurf.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/Windsurf.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI编程开发/Windsurf.png"}, {"id": "coding_010", "name": "Lovable.dev", "description": "Lovable.dev是由GPT-Engineer团队打造的革命性AI全栈开发平台，专注于通过自然语言描述生成完整的Web应用程序。基于先进的大语言模型和代码生成技术，它能够理解用户的自然语言需求并自动生成包含前端界面、后端逻辑、数据库设计的完整应用。核心功能包括自然语言转全栈代码、智能架构设计、自动化部署、实时预览、代码编辑优化、数据库自动配置等。平台支持React、Node.js、TypeScript等现代技术栈，生成的代码遵循最佳实践，结构清晰且易于维护。Lovable.dev提供可视化编辑界面，支持快速迭代和实时调试，集成了版本控制和团队协作功能。开发者为GPT-Engineer团队，专注于降低软件开发的技术门槛。典型应用场景包括快速原型开发、MVP构建、企业内部工具、电商平台、内容管理系统、API服务开发等，特别适合创业者、产品经理、设计师和非技术背景的创新者，无需深入的编程知识即可创建功能完整的现代化Web应用，大幅缩短从想法到产品的开发周期。", "url": "https://lovable.dev", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/Lovable.dev.png", "categoryId": "AI编程开发", "tags": ["魔法"], "localIcon": "图片/AI编程开发/Lovable.dev.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/Lovable.dev.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI编程开发/Lovable.dev.png"}, {"id": "coding_011", "name": "Replit", "description": "Replit是一款功能强大的在线集成开发环境，集成了AI编程助手Ghostwriter，为开发者提供云端的完整编程体验。基于浏览器的开发平台支持50+种编程语言，包括Python、JavaScript、Java、C++、Go、Rust等，无需本地安装即可进行全栈开发。核心功能包括AI代码生成与补全（Ghostwriter）、实时协作编程、云端运行环境、自动化部署、版本控制、包管理等。Ghostwriter AI助手能够提供智能代码建议、错误修复、代码解释、单元测试生成等功能，显著提升编程效率。平台特色是完全基于云端的开发体验，支持即时分享和协作，提供丰富的模板和教程，集成了数据库、Web服务器等开发所需的全部工具。开发者为Replit团队，致力于让编程更加accessible和collaborative。典型应用场景包括编程学习和教育、快速原型开发、团队协作项目、开源项目贡献、技术面试准备、hackathon参与等，特别适合学生、教育工作者、初学者、远程团队和需要快速验证想法的开发者，通过云端环境和AI助手降低编程门槛并促进知识分享。", "url": "https://replit.com", "icon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/Replit.png", "categoryId": "AI编程开发", "tags": ["魔法"], "localIcon": "图片/AI编程开发/Replit.png", "qiniuIcon": "https://naixi.qiniu.logitnote.com/数据20250427/images/AI编程开发/Replit.png", "originalIcon": "cloud://cloud1-1g0spyn6cff5aa03.636c-cloud1-1g0spyn6cff5aa03-1349397796/数据20250427/图片/AI编程开发/Replit.png"}]}