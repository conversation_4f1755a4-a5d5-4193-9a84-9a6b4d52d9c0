/**
 * 七牛云文件夹完整上传脚本
 * 将整个数据文件夹上传到七牛云，不修改任何文件名
 */

const qiniu = require("qiniu");
const fs = require("fs");
const path = require("path");

// 七牛云配置
const qiniuConfig = {
  ACCESS_KEY: "oZsUmpCeCCSL24rbYsH1g3YW6hLMojhEGp4Ua0cb",
  SECRET_KEY: "UhTlav9aMpjGfrLK3kDLGCnvwe8FfN9KNQ1bNmHy",
  BUCKET: "naixi-ai-tools",
  DOMAIN: "naixi.qiniu.logitnote.com",
};

// 数据路径配置
const DATA_CONFIG = {
  DATA_VERSION: "20250427",
  LOCAL_BASE_PATH: path.join(__dirname, "../data/数据20250427"),
  QINIU_BASE_PATH: "数据20250427", // 七牛云中的基础路径
};

// 初始化七牛云配置
const mac = new qiniu.auth.digest.Mac(
  qiniuConfig.ACCESS_KEY,
  qiniuConfig.SECRET_KEY
);

const config = new qiniu.conf.Config();
config.zone = qiniu.zone.Zone_z2; // 华南区域
const formUploader = new qiniu.form_up.FormUploader(config);
const putExtra = new qiniu.form_up.PutExtra();

/**
 * 生成上传凭证
 */
function generateUploadToken(key) {
  const putPolicy = new qiniu.rs.PutPolicy({
    scope: `${qiniuConfig.BUCKET}:${key}`,
  });
  return putPolicy.uploadToken(mac);
}

/**
 * 上传单个文件到七牛云
 */
function uploadFile(localPath, qiniuKey) {
  return new Promise((resolve, reject) => {
    const uploadToken = generateUploadToken(qiniuKey);

    console.log(`📤 开始上传: ${qiniuKey}`);

    formUploader.putFile(
      uploadToken,
      qiniuKey,
      localPath,
      putExtra,
      (respErr, respBody, respInfo) => {
        if (respErr) {
          console.error(`❌ 上传失败: ${qiniuKey}`, respErr);
          reject(respErr);
          return;
        }

        if (respInfo.statusCode === 200) {
          const fileUrl = `https://${qiniuConfig.DOMAIN}/${qiniuKey}`;
          console.log(`✅ 上传成功: ${qiniuKey}`);
          resolve({
            key: qiniuKey,
            url: fileUrl,
            hash: respBody.hash,
          });
        } else {
          console.error(
            `❌ 上传失败: ${qiniuKey}`,
            respInfo.statusCode,
            respBody
          );
          reject(new Error(`上传失败: ${respInfo.statusCode}`));
        }
      }
    );
  });
}

/**
 * 递归获取文件夹中的所有文件
 */
function getAllFiles(dirPath, baseDir = dirPath) {
  const files = [];
  const items = fs.readdirSync(dirPath, { withFileTypes: true });

  for (const item of items) {
    const fullPath = path.join(dirPath, item.name);

    if (item.isDirectory()) {
      // 递归处理子文件夹
      files.push(...getAllFiles(fullPath, baseDir));
    } else if (item.isFile()) {
      // 计算相对路径
      const relativePath = path.relative(baseDir, fullPath);
      files.push({
        localPath: fullPath,
        relativePath: relativePath.replace(/\\/g, "/"), // 统一使用正斜杠
        fileName: item.name,
      });
    }
  }

  return files;
}

/**
 * 批量上传所有文件
 */
async function uploadAllFiles() {
  console.log("📁 开始扫描文件夹...");

  // 获取所有文件
  const allFiles = getAllFiles(DATA_CONFIG.LOCAL_BASE_PATH);

  console.log(`📊 扫描完成，找到 ${allFiles.length} 个文件:`);
  allFiles.forEach((file, index) => {
    console.log(`   ${index + 1}. ${file.relativePath}`);
  });

  const uploadResults = [];
  let successCount = 0;
  let failCount = 0;

  console.log(`\n🚀 开始上传文件...`);

  for (const file of allFiles) {
    try {
      // 构造七牛云中的文件路径
      const qiniuKey = `${DATA_CONFIG.QINIU_BASE_PATH}/${file.relativePath}`;

      const result = await uploadFile(file.localPath, qiniuKey);
      uploadResults.push({
        ...file,
        ...result,
        success: true,
      });
      successCount++;

      // 添加小延迟，避免请求过快
      await new Promise((resolve) => setTimeout(resolve, 100));
    } catch (error) {
      console.error(`❌ ${file.relativePath} 上传失败:`, error.message);
      uploadResults.push({
        ...file,
        success: false,
        error: error.message,
      });
      failCount++;
    }
  }

  console.log(`\n📊 上传完成统计:`);
  console.log(`   成功: ${successCount} 个`);
  console.log(`   失败: ${failCount} 个`);
  console.log(`   总计: ${allFiles.length} 个`);

  if (failCount > 0) {
    console.log(`\n❌ 上传失败的文件:`);
    uploadResults
      .filter((r) => !r.success)
      .forEach((r, index) => {
        console.log(`   ${index + 1}. ${r.relativePath} - ${r.error}`);
      });
  }

  return uploadResults;
}

/**
 * 主上传流程
 */
async function main() {
  console.log("🚀 开始完整上传数据文件夹到七牛云...");
  console.log(`📊 配置信息:`);
  console.log(`   存储空间: ${qiniuConfig.BUCKET}`);
  console.log(`   CDN域名: ${qiniuConfig.DOMAIN}`);
  console.log(`   本地路径: ${DATA_CONFIG.LOCAL_BASE_PATH}`);
  console.log(`   七牛云路径: ${DATA_CONFIG.QINIU_BASE_PATH}`);

  try {
    // 检查本地文件夹是否存在
    if (!fs.existsSync(DATA_CONFIG.LOCAL_BASE_PATH)) {
      throw new Error(`本地文件夹不存在: ${DATA_CONFIG.LOCAL_BASE_PATH}`);
    }

    // 上传所有文件
    const results = await uploadAllFiles();

    console.log("\n🎉 上传完成总结:");
    console.log(`   成功文件: ${results.filter((r) => r.success).length} 个`);
    console.log(`   失败文件: ${results.filter((r) => !r.success).length} 个`);
    console.log(
      `   CDN基础地址: https://${qiniuConfig.DOMAIN}/${DATA_CONFIG.QINIU_BASE_PATH}/`
    );

    // 输出一些示例URL
    const successFiles = results.filter((r) => r.success);
    if (successFiles.length > 0) {
      console.log(`\n🔗 示例访问地址:`);
      successFiles.slice(0, 3).forEach((file, index) => {
        console.log(`   ${index + 1}. ${file.url}`);
      });
      if (successFiles.length > 3) {
        console.log(`   ... 还有 ${successFiles.length - 3} 个文件`);
      }
    }
  } catch (error) {
    console.error("❌ 上传流程失败:", error);
    process.exit(1);
  }
}

// 检查配置
function checkConfig() {
  const requiredKeys = ["ACCESS_KEY", "SECRET_KEY", "BUCKET", "DOMAIN"];
  const missingKeys = requiredKeys.filter(
    (key) => !qiniuConfig[key] || qiniuConfig[key].startsWith("your_")
  );

  if (missingKeys.length > 0) {
    console.error("❌ 请先配置七牛云参数:");
    missingKeys.forEach((key) => {
      console.error(`   ${key}: ${qiniuConfig[key]}`);
    });
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  checkConfig();
  main().catch(console.error);
}

module.exports = {
  uploadFile,
  getAllFiles,
  uploadAllFiles,
};
