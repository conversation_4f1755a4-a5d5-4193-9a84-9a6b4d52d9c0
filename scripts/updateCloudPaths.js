/**
 * 更新JSON文件中的图片路径，将localIcon转换为云存储路径
 */
const fs = require("fs");
const path = require("path");

// 配置
const DATA_VERSION = "20250427";
const CLOUD_ENV_ID = "cloud1-1g0spyn6cff5aa03";
const JSON_FILE_PATH = `miniprogram/data/数据${DATA_VERSION}/tools_new_${DATA_VERSION}.json`;

// 构建云存储基础路径
const CLOUD_BASE_PATH = `cloud://${CLOUD_ENV_ID}.636c-${CLOUD_ENV_ID}-1349397796/数据${DATA_VERSION}`;

console.log("开始更新JSON文件中的图片路径...");
console.log("文件路径:", JSON_FILE_PATH);
console.log("云存储基础路径:", CLOUD_BASE_PATH);

try {
  // 读取JSON文件
  const jsonData = JSON.parse(fs.readFileSync(JSON_FILE_PATH, "utf8"));

  if (!jsonData.tools || !Array.isArray(jsonData.tools)) {
    throw new Error("JSON文件格式不正确，缺少tools数组");
  }

  let updateCount = 0;

  // 更新每个工具的图片路径
  jsonData.tools.forEach((tool, index) => {
    if (tool.localIcon && (!tool.icon || tool.icon === "")) {
      // 从localIcon构建云存储路径
      const cloudPath = `${CLOUD_BASE_PATH}/${tool.localIcon}`;
      tool.icon = cloudPath;
      updateCount++;

      console.log(
        `[${index + 1}] ${tool.name}: ${tool.localIcon} -> ${cloudPath}`
      );
    }
  });

  // 写回文件
  fs.writeFileSync(JSON_FILE_PATH, JSON.stringify(jsonData, null, 2), "utf8");

  console.log(`\n✅ 完成！共更新了 ${updateCount} 个工具的图片路径`);
  console.log("请重新上传JSON文件到云存储");
} catch (error) {
  console.error("❌ 更新失败:", error.message);
  process.exit(1);
}
