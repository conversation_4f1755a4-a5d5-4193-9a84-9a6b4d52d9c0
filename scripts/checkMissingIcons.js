const fs = require("fs");
const path = require("path");

// 读取JSON文件
const jsonPath = "../miniprogram/data/数据20250427/tools_new_20250427.json";
const data = JSON.parse(fs.readFileSync(jsonPath, "utf8"));

// 图标目录
const iconDir = "../miniprogram/data/数据20250427/图片";

console.log("🔍 检查工具图标完整性...\n");

let missingIcons = [];
let totalTools = 0;

data.tools.forEach((tool) => {
  totalTools++;
  const iconPath = tool.localIcon;
  if (iconPath) {
    const fullPath = path.join(iconDir, iconPath.replace("图片/", ""));
    if (!fs.existsSync(fullPath)) {
      missingIcons.push({
        name: tool.name,
        category: tool.categoryId,
        expectedPath: iconPath,
        fullPath: fullPath,
      });
    }
  } else {
    missingIcons.push({
      name: tool.name,
      category: tool.categoryId,
      expectedPath: "未定义",
      fullPath: "未定义",
    });
  }
});

console.log(`📊 检查结果:`);
console.log(`- 总工具数: ${totalTools}`);
console.log(`- 缺少图标: ${missingIcons.length}`);
console.log(
  `- 完整率: ${(
    ((totalTools - missingIcons.length) / totalTools) *
    100
  ).toFixed(1)}%`
);

if (missingIcons.length > 0) {
  console.log(`\n❌ 缺少图标的工具:`);
  missingIcons.forEach((item, index) => {
    console.log(`${index + 1}. ${item.name} (${item.category})`);
    console.log(`   期望路径: ${item.expectedPath}`);
    if (item.fullPath !== "未定义") {
      console.log(`   完整路径: ${item.fullPath}`);
    }
  });
} else {
  console.log(`\n✅ 所有工具都有图标！`);
}
