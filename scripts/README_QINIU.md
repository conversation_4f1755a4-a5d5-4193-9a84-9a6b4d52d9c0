# 七牛云上传指南

## 准备工作

### 1. 注册七牛云账号
1. 访问 [七牛云官网](https://www.qiniu.com/)
2. 注册账号并完成实名认证

### 2. 创建存储空间
1. 登录 [七牛云控制台](https://portal.qiniu.com/)
2. 进入「对象存储」-> 「空间管理」
3. 点击「新建存储空间」
4. 填写配置：
   - **存储空间名称**: 如 `ai-tools-bucket`
   - **存储区域**: 选择离用户最近的区域
   - **访问控制**: 选择「公开空间」（方便CDN访问）

### 3. 配置CDN加速域名
1. 在存储空间中点击「域名管理」
2. 点击「绑定域名」
3. 选择：
   - **加速域名**: 填入你的域名（如 `cdn.yourdomain.com`）
   - **或使用七牛提供的测试域名**（30天有效期）

### 4. 获取密钥信息
1. 点击右上角头像 -> 「密钥管理」
2. 记录下：
   - **AccessKey** 
   - **SecretKey**

## 上传步骤

### 1. 安装依赖
\`\`\`bash
cd scripts
npm install
\`\`\`

### 2. 配置七牛云参数
编辑 `scripts/uploadToQiniu.js` 文件，替换配置信息：

\`\`\`javascript
const qiniuConfig = {
  ACCESS_KEY: 'your_access_key',    // 替换为你的AccessKey
  SECRET_KEY: 'your_secret_key',    // 替换为你的SecretKey  
  BUCKET: 'ai-tools-bucket',        // 替换为你的存储空间名称
  DOMAIN: 'your-cdn-domain.com',    // 替换为你的CDN域名
};
\`\`\`

### 3. 执行上传
\`\`\`bash
cd scripts
npm run upload-qiniu
\`\`\`

## 上传内容

脚本会自动上传：

### 📁 目录结构（七牛云中）
\`\`\`
ai-tools/data20250427/
├── tools_qiniu_20250427.json    # 更新了图片路径的JSON文件
├── tools_new_20250427.json      # 原始JSON文件
└── images/                      # 图片文件
    ├── AI聊天/
    │   ├── ChatGPT.png
    │   ├── Claude.png
    │   └── ...
    ├── AI图像/
    └── ...
\`\`\`

### 📊 上传内容
- **1个JSON文件** (原始版本)
- **1个JSON文件** (七牛云版本，图片路径已更新)  
- **~109个PNG图标** (按分类分文件夹)

## 上传完成后

### 🔗 访问地址示例
- JSON数据：`https://your-cdn-domain.com/ai-tools/data20250427/tools_qiniu_20250427.json`
- 图标示例：`https://your-cdn-domain.com/ai-tools/data20250427/images/AI聊天/ChatGPT.png`

### ⚙️ 更新小程序配置
脚本会自动生成配置建议，复制到 `miniprogram/config/config.js` 中。

## 费用参考

七牛云对象存储费用很低：
- **存储费用**: ~0.1GB 数据，每月约 ¥0.02
- **CDN流量**: 根据访问量计费，每GB约 ¥0.18
- **HTTP请求**: 每万次约 ¥0.01

总费用每月预估 < ¥5 （适合个人项目）

## 故障排除

### 问题1: 上传失败
- 检查AccessKey和SecretKey是否正确
- 确认存储空间名称无误
- 检查网络连接

### 问题2: CDN域名访问不了
- 确认域名已完成备案（如使用国内CDN）
- 检查域名解析是否正确
- 可先使用七牛提供的测试域名

### 问题3: 图片显示不了
- 确认存储空间访问权限为「公开」
- 检查图片URL是否正确
- 查看浏览器控制台错误信息