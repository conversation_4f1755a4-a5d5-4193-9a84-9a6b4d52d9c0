// 云函数入口文件
const cloud = require("wx-server-sdk");

// 云环境ID
const CLOUD_ENV_ID = "cloud1-1g0spyn6cff5aa03"; // 替换为你的云环境ID

// 初始化云环境
cloud.init({
  env: CLOUD_ENV_ID,
});

/**
 * 从云存储获取JSON文件内容
 * @param {Object} event - 云函数调用参数
 * @param {string} event.filePath - 云存储中的文件路径，如 '数据20250427/tools_new_20250427.json'
 * @param {string} event.operation - 操作类型，可选值: 'getJson'(默认), 'getIcons'
 * @param {Array} event.iconPaths - 当 operation 为 'getIcons' 时，需要获取临时链接的图标路径数组
 * @param {string} event.envId - 可选，云环境ID，默认使用初始化的环境ID
 */
exports.main = async (event, context) => {
  const {
    filePath,
    operation = "getJson",
    iconPaths = [],
    envId = CLOUD_ENV_ID,
  } = event;

  console.log(
    `云函数调用，操作: ${operation}, 环境ID: ${envId}, 文件路径: ${filePath}`
  );

  // 获取图标临时链接
  if (operation === "getIcons") {
    try {
      if (!iconPaths || !Array.isArray(iconPaths) || iconPaths.length === 0) {
        return {
          success: false,
          error: "缺少图标路径参数或参数格式不正确",
          data: null,
        };
      }

      console.log(`开始获取图标临时链接，共 ${iconPaths.length} 个图标`);
      console.log("第一个路径示例:", iconPaths[0]);

      // 构建文件ID列表，不进行任何编码处理
      const validFileIDs = [];

      for (let i = 0; i < iconPaths.length; i++) {
        const path = iconPaths[i];
        console.log(`处理图标[${i}]: ${path}`);

        // 验证文件路径格式
        if (
          !path ||
          typeof path !== "string" ||
          path.indexOf("cloud://") !== 0
        ) {
          console.error(`图标路径[${i}]格式无效: ${path}`);
          continue;
        }

        // 直接使用原始路径，不进行编码
        validFileIDs.push({
          fileID: path,
          maxAge: 86400, // 临时链接有效期一天
        });
      }

      if (validFileIDs.length === 0) {
        return {
          success: false,
          error: "没有有效的图标文件ID",
          data: [],
        };
      }

      console.log(`验证后的有效图标文件数: ${validFileIDs.length}`);

      // ✨ 分批处理逻辑，解决微信云开发50个文件限制
      console.log("开始分批处理图标文件，每批最多50个...");
      const batchSize = 50;
      const allResults = [];
      const totalBatches = Math.ceil(validFileIDs.length / batchSize);

      console.log(
        `总共需要处理 ${totalBatches} 批，每批最多 ${batchSize} 个文件`
      );

      for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
        const startIndex = batchIndex * batchSize;
        const endIndex = Math.min(startIndex + batchSize, validFileIDs.length);
        const currentBatch = validFileIDs.slice(startIndex, endIndex);

        console.log(
          `处理第 ${batchIndex + 1}/${totalBatches} 批：文件 ${
            startIndex + 1
          }-${endIndex}`
        );

        try {
          // 直接使用原始文件ID，不进行任何编码处理
          console.log(`批次${batchIndex + 1}包含${currentBatch.length}个文件:`);
          currentBatch.forEach((fileItem, index) => {
            console.log(`  [${index}] ${fileItem.fileID}`);
          });

          // 调用微信云开发API获取当前批次的临时链接
          const batchResult = await cloud.getTempFileURL({
            fileList: currentBatch,
          });

          if (!batchResult || !batchResult.fileList) {
            console.error(
              `第${batchIndex + 1}批次getTempFileURL返回结果无效:`,
              batchResult
            );
            // 为当前批次的文件添加错误状态
            const errorResults = currentBatch.map((item) => ({
              fileID: item.fileID,
              status: -1,
              errMsg: "批次处理失败：返回结果无效",
            }));
            allResults.push(...errorResults);
            continue;
          }

          // 检查当前批次结果中的错误
          const batchFileList = batchResult.fileList || [];
          console.log(
            `第${batchIndex + 1}批次获取临时链接结果: 共${
              batchFileList.length
            }个文件`
          );

          for (let i = 0; i < batchFileList.length; i++) {
            const file = batchFileList[i];
            if (file.status !== 0) {
              console.error(
                `批次${batchIndex + 1}，文件[${i}] ${
                  file.fileID
                } 获取临时链接失败: 状态=${file.status}, 错误=${file.errMsg}`
              );
            } else {
              console.log(
                `批次${batchIndex + 1}，文件[${i}] 获取临时链接成功: ${
                  file.tempFileURL
                }`
              );
            }
          }

          // 将当前批次结果添加到总结果中
          allResults.push(...batchFileList);

          // 添加批次间的延迟，避免并发过高
          if (batchIndex < totalBatches - 1) {
            console.log("批次间延迟100ms...");
            await new Promise((resolve) => setTimeout(resolve, 100));
          }
        } catch (batchError) {
          console.error(`第${batchIndex + 1}批次处理失败:`, batchError);
          // 为当前批次的文件添加错误状态
          const errorResults = currentBatch.map((item) => ({
            fileID: item.fileID,
            status: -1,
            errMsg: `批次处理异常: ${batchError.message}`,
          }));
          allResults.push(...errorResults);
        }
      }

      console.log(`所有批次处理完成，总计处理了 ${allResults.length} 个文件`);

      // 统计成功和失败的数量
      const successCount = allResults.filter(
        (file) => file.status === 0
      ).length;
      const failCount = allResults.length - successCount;
      console.log(
        `处理结果统计：成功 ${successCount} 个，失败 ${failCount} 个`
      );

      return {
        success: true,
        data: allResults,
        error: null,
        debug: {
          totalFiles: validFileIDs.length,
          totalBatches: totalBatches,
          successCount: successCount,
          failCount: failCount,
        },
      };
    } catch (error) {
      console.error("获取图标临时链接失败:", error);
      return {
        success: false,
        error: error.message || "获取图标临时链接失败",
        data: null,
      };
    }
  }

  // 以下是获取JSON数据的默认操作
  if (!filePath) {
    console.error("错误：缺少文件路径参数");
    return {
      success: false,
      error: "缺少文件路径参数",
      data: null,
    };
  }

  try {
    console.log(`开始从云存储读取文件: ${filePath}`);

    // 检查filePath是否包含数据版本文件夹
    const pathParts = filePath.split("/");
    const hasDataVersionFolder = pathParts.some(
      (part) =>
        part.startsWith("数据") &&
        part.length === 10 &&
        !isNaN(part.substring(2))
    );

    if (hasDataVersionFolder) {
      console.log(`✓ 文件路径包含数据版本文件夹: ${filePath}`);
    } else {
      console.log(`⚠️ 文件路径不包含数据版本文件夹: ${filePath}`);
    }

    // 构建完整的云存储文件ID
    const fileID = `cloud://${envId}.636c-${envId}-1349397796/${filePath}`;
    console.log(`构建的完整云存储文件ID: ${fileID}`);

    // 1. 先检查文件是否存在
    console.log("步骤1: 检查文件是否存在...");
    try {
      const statResult = await cloud.file().stat({
        fileID: fileID,
      });
      console.log("✓ 文件存在性检查结果:", statResult);
    } catch (statError) {
      console.error("⚠️ 文件存在性检查失败:", statError);
      console.error("这可能不影响后续操作，继续尝试获取临时链接...");
    }

    // 2. 获取文件临时链接
    console.log("步骤2: 获取文件临时链接...");
    const fileResult = await cloud.getTempFileURL({
      fileList: [
        {
          fileID: fileID,
          maxAge: 86400, // 临时链接有效期一天
        },
      ],
    });

    console.log(
      "getTempFileURL 完整返回结果:",
      JSON.stringify(fileResult, null, 2)
    );

    if (!fileResult.fileList || fileResult.fileList.length === 0) {
      console.error("❌ getTempFileURL返回的fileList为空或不存在");
      return {
        success: false,
        error: "获取文件临时链接失败: fileList为空",
        data: null,
        debug: {
          fileID: fileID,
          fileResult: fileResult,
        },
      };
    }

    const firstFile = fileResult.fileList[0];
    if (firstFile.status !== 0) {
      console.error("❌ 获取文件临时链接失败:", {
        status: firstFile.status,
        errMsg: firstFile.errMsg,
        fileID: firstFile.fileID,
      });
      return {
        success: false,
        error: `获取文件临时链接失败: ${firstFile.errMsg || "未知错误"}`,
        data: null,
        debug: {
          fileID: fileID,
          status: firstFile.status,
          errMsg: firstFile.errMsg,
        },
      };
    }

    const tempFileURL = firstFile.tempFileURL;
    console.log(`✓ 成功获取到文件临时链接: ${tempFileURL}`);

    // 3. 云函数中下载文件内容（不受域名限制）
    console.log("步骤3: 下载文件内容...");
    const axios = require("axios");

    console.log("开始使用axios下载文件...");
    console.log("原始临时URL:", tempFileURL);

    // 对URL进行编码处理，解决中文字符问题（仅针对axios请求）
    const encodedURL = encodeURI(tempFileURL);
    console.log("编码后的URL:", encodedURL);

    const response = await axios.get(encodedURL, {
      timeout: 30000, // 30秒超时
      headers: {
        "User-Agent": "WeChat-MiniProgram CloudFunction",
      },
    });

    console.log(`✓ axios响应状态: ${response.status}`);
    console.log(`✓ 响应数据类型: ${typeof response.data}`);

    if (!response || !response.data) {
      console.error("❌ axios响应无效或数据为空");
      return {
        success: false,
        error: "下载文件内容失败: 响应数据为空",
        data: null,
        debug: {
          responseStatus: response?.status,
          responseDataType: typeof response?.data,
        },
      };
    }

    // 检查是否为有效的JSON数据
    if (typeof response.data === "object") {
      console.log(
        `✓ 成功获取JSON数据，categories数量: ${
          response.data.categories?.length || 0
        }, tools数量: ${response.data.tools?.length || 0}`
      );
    } else if (typeof response.data === "string") {
      console.log("⚠️ 获取的数据是字符串格式，尝试解析为JSON...");
      try {
        const jsonData = JSON.parse(response.data);
        console.log(
          `✓ 成功解析JSON数据，categories数量: ${
            jsonData.categories?.length || 0
          }, tools数量: ${jsonData.tools?.length || 0}`
        );
        response.data = jsonData;
      } catch (parseError) {
        console.error("❌ JSON解析失败:", parseError.message);
        return {
          success: false,
          error: `JSON解析失败: ${parseError.message}`,
          data: null,
          debug: {
            dataType: typeof response.data,
            dataPreview: String(response.data).substring(0, 200),
          },
        };
      }
    }

    // 4. 返回文件内容
    console.log("✓ 云函数执行成功，返回数据");
    return {
      success: true,
      data: response.data,
      error: null,
    };
  } catch (error) {
    console.error("❌ 云函数执行出现异常:", error);
    console.error("错误详情:", {
      name: error.name,
      message: error.message,
      stack: error.stack,
      code: error.code,
    });

    return {
      success: false,
      error: error.message || "云函数执行错误",
      data: null,
      debug: {
        errorName: error.name,
        errorCode: error.code,
        filePath: filePath,
        envId: envId,
      },
    };
  }
};
