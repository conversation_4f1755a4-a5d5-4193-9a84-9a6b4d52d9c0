// app.js
// const toolsDataManager = require("./utils/toolsDataManager");
// const toolsDataManager = require("./utils/qiniuDataManager");
// const toolsDataManager = require("./utils/cloudManager");
// const toolsDataManager = require("./utils/jsonCloudManager");
// const toolsDataManager = require("./utils/cloudFunctionManager");

// 使用数据管理器工厂
const dataManagerFactory = require("./utils/dataManagerFactory");
const toolsDataManager = dataManagerFactory.getDataManager();

// 获取配置
const config = require("./config/config");
const CLOUD_ENV_ID = dataManagerFactory.getCloudEnvId();

App({
  onLaunch: function () {
    // 检查数据源类型
    const dataSourceType = dataManagerFactory.getDataSourceType();
    console.log(`当前数据源类型: ${dataSourceType}`);

    // 如果使用云端数据源，则初始化云环境
    if (dataSourceType === "cloud") {
      if (!wx.cloud) {
        console.error("请使用 2.2.3 或以上的基础库以使用云能力");
      } else {
        try {
          wx.cloud.init({
            // env 参数说明：
            //   env 参数决定接下来小程序发起的云开发调用（wx.cloud.xxx）会默认请求到哪个云环境的资源
            //   此处请填入环境 ID, 环境 ID 可打开云控制台查看
            //   如不填则使用默认环境（第一个创建的环境）
            env: CLOUD_ENV_ID,
            traceUser: true,
          });
          console.log(`云环境初始化成功: ${CLOUD_ENV_ID}`);
        } catch (error) {
          console.error("云环境初始化失败:", error);
        }
      }
    } else {
      console.log("使用本地数据源，跳过云环境初始化");
    }

    // 初始化全局数据
    this.globalData = {
      cloudEnvId: CLOUD_ENV_ID,
      dataSourceType: dataSourceType,
      categories: [],
      userInfo: null,
    };

    // 初始化工具数据
    this.initToolsData();
  },

  initToolsData() {
    // 显示加载提示
    wx.showLoading({
      title: "加载中...",
      mask: true,
    });

    toolsDataManager
      .initToolsData()
      .then((success) => {
        console.log("工具数据初始化", success ? "成功" : "部分失败");

        // 初始化成功后，预加载分类数据
        return toolsDataManager.getCategories();
      })
      .then((categories) => {
        if (!categories || !Array.isArray(categories)) {
          console.error("预加载的分类数据无效");
          return Promise.reject(new Error("分类数据无效"));
        }

        console.log("预加载分类数据成功:", categories.length);
        this.globalData.categories = categories;

        // 隐藏加载提示
        wx.hideLoading();
      })
      .catch((error) => {
        console.error("工具数据初始化失败:", error);

        // 隐藏加载提示并显示错误toast
        wx.hideLoading();
        wx.showToast({
          title: "数据加载失败",
          icon: "none",
          duration: 2000,
        });
      });
  },

  // 获取云环境ID
  getCloudEnvId() {
    return CLOUD_ENV_ID;
  },
});

{
  "pages": [
    "pages/index/index",
    "pages/tool-detail/index",
    "pages/web/index",
    "pages/about/about",
    "pages/cache-manager/index"
  ],
  "window": {
    "backgroundColor": "#F6F6F6",
    "backgroundTextStyle": "light",
    "navigationBarBackgroundColor": "#fff",
    "navigationBarTitleText": "奶昔的AI工具箱",
    "navigationBarTextStyle": "black"
  },
  "networkTimeout": {
    "request": 10000,
    "downloadFile": 10000
  },
  "sitemapLocation": "sitemap.json",
  "style": "v2",
  "lazyCodeLoading": "requiredComponents",
  "cloud": true,
  "tabBar": {
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页",
        "iconPath": "images/placeholder/icon/首页.png",
        "selectedIconPath": "images/placeholder/icon/首页_选中.png"
      },
      {
        "pagePath": "pages/about/about",
        "text": "关于",
        "iconPath": "images/placeholder/icon/关于.png",
        "selectedIconPath": "images/placeholder/icon/关于_选中.png"
      }
    ]
  }
}

/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

/* 占位图样式 */
.placeholder-icon {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 12rpx;
  color: #999;
}

.placeholder-text {
  font-size: 24rpx;
  color: #999;
}

button {
  background: initial;
}

button:focus {
  outline: 0;
}

button::after {
  border: none;
}


page {
  background: #f6f6f6;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
/**
 * 小程序配置文件
 */

// 数据版本配置
const DATA_VERSION = {
  // 数据版本日期，格式为YYYYMMDD，用于指定使用哪个日期文件夹的数据
  DATE: "20250427",
};

// 数据源配置
const DATA_SOURCE = {
  // 数据源类型: 'local' (本地) 或 'cloud' (云端)
  TYPE: "cloud", // 修改为云端
  // 如果云端有问题，临时回滚请改为: TYPE: "local"

  // 云环境配置
  CLOUD: {
    ENV_ID: "your-cloud-env-id",
    // 使用数据版本日期构造路径
    BASE_PATH: `数据${DATA_VERSION.DATE}`,
    TOOLS_JSON_PATH: `数据${DATA_VERSION.DATE}/tools_new_${DATA_VERSION.DATE}.json`,
    IMAGES_BASE_PATH: `数据${DATA_VERSION.DATE}/图片`,
  },

  // 本地文件配置
  LOCAL: {
    // JSON文件路径不带开头斜杠（用于文件系统读取）
    TOOLS_JSON_PATH: `data/数据${DATA_VERSION.DATE}/tools_new_${DATA_VERSION.DATE}.json`,
    // 图片路径带开头斜杠（用于图片显示，相对于小程序根目录的绝对路径）
    BASE_PATH: `/data/数据${DATA_VERSION.DATE}`,
    IMAGES_BASE_PATH: `/data/数据${DATA_VERSION.DATE}/图片`,
  },
};

// 图片缓存配置
const IMAGE_CACHE = {
  // 最大缓存大小（字节）
  MAX_CACHE_SIZE: 50 * 1024 * 1024, // 50MB

  // 缓存过期天数
  CACHE_EXPIRE_DAYS: 7,

  // 最大并发下载数
  MAX_CONCURRENT_DOWNLOADS: 3,

  // 缓存目录名
  CACHE_DIR_NAME: "imageCache",

  // 元数据存储键
  METADATA_KEY: "imageCacheMetadata",

  // 是否启用图片缓存（仅云端模式有效）
  ENABLED: true,

  // 缓存清理策略
  CLEANUP: {
    // 启动时是否自动清理过期缓存
    AUTO_CLEANUP_ON_START: true,

    // 缓存大小超过限制时清理到的目标比例
    TARGET_SIZE_RATIO: 0.8, // 清理到80%
  },

  // 默认占位符图片路径
  PLACEHOLDER_IMAGE: "/images/placeholder/tool-icon.png",
};

module.exports = {
  DATA_SOURCE,
  DATA_VERSION,
  IMAGE_CACHE,
};

// pages/index/index.js
// 获取全局应用实例，可以用来获取全局数据或调用全局方法
const app = getApp();
// 引入数据管理器工厂和配置
const dataManagerFactory = require("../../utils/dataManagerFactory");
const toolsDataManager = dataManagerFactory.getDataManager();
const config = require("../../config/config");

// 定义页面
Page({
  // 页面的初始数据
  data: {
    categories: [], // 用于存储所有工具分类的数组
    currentTab: 0, // 当前激活的顶部标签页的索引，默认为第一个
    currentCategory: "", // 当前选中的分类的唯一标识符 (ID)
    currentProducts: [], // 当前选中分类下需要展示的工具产品列表
    tabScrollLeft: 0, // 恢复：标签栏的水平滚动位置
    dataSourceType: "", // 当前使用的数据源类型
    categoryProducts: {}, // 存储所有分类的产品数据，用于缓存已加载的数据
    showSwipeHint: false, // 是否显示滑动提示
  },

  // 页面加载时触发的生命周期函数
  onLoad() {
    // 设置数据源类型
    const dataSourceType = dataManagerFactory.getDataSourceType();
    this.setData({ dataSourceType });
    console.log(`当前页面数据源类型: ${dataSourceType}`);

    // 调用 loadData 方法加载页面所需数据
    this.loadData();

    // 每次打开都显示滑动提示
    // 延迟1秒显示滑动提示，等待页面加载完成
    setTimeout(() => {
      this.setData({ showSwipeHint: true });
      // 3秒后自动关闭提示
      setTimeout(() => {
        this.setData({ showSwipeHint: false });
      }, 2000);
    }, 1000);

    // 如果使用云端数据源，确保云环境已初始化
    if (dataSourceType === "cloud") {
      if (!wx.cloud) {
        console.error("云开发环境未初始化");
        wx.showToast({
          title: "云环境初始化失败",
          icon: "none",
        });
        return;
      }

      // 初始化云环境
      wx.cloud.init({
        env: dataManagerFactory.getCloudEnvId(),
        traceUser: true,
      });
    }
  },

  // 页面显示/切入前台时触发的生命周期函数
  onShow() {
    // 此处可以添加页面每次显示时需要执行的逻辑，例如刷新数据
    // 当前为空，没有特定逻辑
  },

  // 定义加载页面初始数据的方法
  loadData() {
    // 显示加载提示框，提升用户体验
    wx.showLoading({
      title: "加载中...", // 提示框文字内容
    });

    // 异步获取所有工具分类
    toolsDataManager
      .getCategories()
      .then((categories) => {
        // 成功获取分类数据后的回调
        // 检查获取到的分类数据是否有效
        if (!categories || !categories.length) {
          // 如果分类数据为空或无效，隐藏加载提示框
          wx.hideLoading();
          // 返回一个被拒绝的 Promise，中断后续链式调用，并传递错误信息
          return Promise.reject(new Error("分类数据为空"));
        }

        // 获取第一个分类的 ID，如果分类数组为空则设为空字符串
        const firstCategory = categories[0]?.id || "";
        // 更新页面的 data，设置分类列表和当前选中的分类（默认为第一个）
        this.setData({ categories, currentCategory: firstCategory });
        // 在控制台打印加载到的分类数据，方便调试
        console.log("加载的分类数据:", categories);

        // 基于第一个分类的 ID，异步获取该分类下的工具列表
        return toolsDataManager.getToolsByCategory(firstCategory);
      })
      .then((products) => {
        // 成功获取第一个分类的工具数据后的回调
        // 隐藏加载提示框
        wx.hideLoading();

        // 添加更详细的日志，特别是检查图片URL
        if (products.length > 0) {
          console.log(`首个产品加载成功，图标URL: ${products[0].icon}`);
          // 记录所有图片路径，用于调试
          products.forEach((product, index) => {
            console.log(
              `索引 ${index}, 产品名称: ${product.name}, 图片路径: ${
                product.icon
              }, 是否缓存: ${product.isCached || false}`
            );
          });
        }

        // 在控制台打印加载到的产品数据，方便调试
        console.log("加载的产品数据:", products);
        // 打印第一个产品的示例信息，或提示无产品数据
        console.log(
          "第一个产品示例:",
          products.length > 0 ? products[0] : "无产品数据"
        );

        // 图片已在数据管理器中处理完成，直接使用
        this.setData({
          currentProducts: products, // 设置当前分类的产品列表
        });
      })
      .catch((error) => {
        // 捕获在获取分类或工具数据过程中发生的任何错误
        // 在控制台打印错误信息，方便调试
        console.error("加载数据失败:", error);
        // 隐藏加载提示框
        wx.hideLoading();
        // 显示一个轻提示，告知用户加载失败
        wx.showToast({
          title: "加载失败，请重试", // 提示文字
          icon: "none", // 不显示图标
        });
      });
  },

  // 定义切换顶部标签页的方法
  switchTab(e) {
    const id = e.currentTarget.dataset.id; // 获取被点击标签对应的分类 ID
    const index = e.currentTarget.dataset.index; // 获取被点击标签的索引

    if (this.data.currentTab === index) {
      return;
    }

    this.setData({
      currentTab: index,
      currentCategory: id,
    });

    this.ensureTabVisible(index);

    this.loadCategoryData(id, index);
    this.preloadAdjacentCategories(index);
  },

  // 处理swiper切换事件
  handleSwiperChange(e) {
    const index = e.detail.current;
    console.log(`[handleSwiperChange] Swiper changed to index: ${index}`);
    if (!this.data.categories || index >= this.data.categories.length) {
      console.error(
        `[handleSwiperChange] Invalid index ${index} or categories data missing.`
      );
      return;
    }
    const id = this.data.categories[index].id;
    console.log(`[handleSwiperChange] Corresponding category ID: ${id}`);

    // 不再需要在 nextTick 中设置 scrollIntoView
    this.setData({
      currentTab: index,
      currentCategory: id,
    });

    this.ensureTabVisible(index);

    this.loadCategoryData(id, index);
    this.preloadAdjacentCategories(index);
  },

  // 加载指定分类的数据
  loadCategoryData(categoryId, index) {
    // 检查是否已缓存该分类的数据
    if (this.data.categoryProducts[categoryId]) {
      // 如果已缓存，直接使用缓存数据
      this.setData({
        currentProducts: this.data.categoryProducts[categoryId],
      });

      // 预加载相邻分类数据
      this.preloadAdjacentCategories(index);
      return;
    }

    // 否则，加载该分类的数据
    wx.showLoading({ title: "加载中..." });

    toolsDataManager
      .getToolsByCategory(categoryId)
      .then((products) => {
        wx.hideLoading();

        // 数据管理器已处理图片缓存，直接使用
        // 缓存该分类的数据
        const categoryProducts = { ...this.data.categoryProducts };
        categoryProducts[categoryId] = products;

        // 更新页面数据
        this.setData({
          currentProducts: products,
          categoryProducts,
        });

        // 预加载相邻分类数据
        this.preloadAdjacentCategories(index);
      })
      .catch((error) => {
        console.error(`加载分类 ${categoryId} 的数据失败:`, error);
        wx.hideLoading();
        wx.showToast({
          title: "加载失败，请重试",
          icon: "none",
        });
      });
  },

  // 预加载相邻分类的数据
  preloadAdjacentCategories(currentIndex) {
    const { categories } = this.data;

    // 预加载下一个分类（如果存在）
    if (currentIndex < categories.length - 1) {
      const nextCategory = categories[currentIndex + 1];
      this.preloadCategoryData(nextCategory.id);
    }

    // 预加载上一个分类（如果存在）
    if (currentIndex > 0) {
      const prevCategory = categories[currentIndex - 1];
      this.preloadCategoryData(prevCategory.id);
    }
  },

  // 预加载分类数据（不显示加载提示，在后台静默加载）
  preloadCategoryData(categoryId) {
    // 如果已经缓存，则不需要预加载
    if (this.data.categoryProducts[categoryId]) {
      return;
    }

    // 静默加载数据
    toolsDataManager
      .getToolsByCategory(categoryId)
      .then((products) => {
        // 数据管理器已处理图片缓存，直接使用
        // 缓存该分类的数据
        const categoryProducts = { ...this.data.categoryProducts };
        categoryProducts[categoryId] = products;

        // 更新缓存数据
        this.setData({ categoryProducts });
        console.log(`预加载分类 ${categoryId} 数据成功`);
      })
      .catch((error) => {
        console.error(`预加载分类 ${categoryId} 数据失败:`, error);
      });
  },

  // 定义跳转到工具详情页面的方法
  goToDetail(e) {
    // 从事件对象中获取触发事件的组件上绑定的自定义数据
    const index = e.currentTarget.dataset.index; // 获取被点击项在其列表中的索引
    // 根据索引从当前产品列表 (currentProducts) 中获取对应的工具对象
    const tool = this.data.currentProducts[index];
    // 在控制台打印被点击的产品信息，方便调试
    console.log("点击的产品:", tool);
    // 打印即将用于跳转的 tool.id
    console.log("准备跳转，产品 ID:", tool.id);
    // 使用微信小程序的导航 API 跳转到详情页面
    wx.navigateTo({
      // 指定目标页面的路径，并通过 URL 参数传递工具的 ID
      // 使用 encodeURIComponent 对 ID 进行编码，防止特殊字符引起问题
      url: `/pages/tool-detail/index?id=${encodeURIComponent(tool.id)}`,
    });
  },

  // 处理图片加载错误
  handleImageError(e) {
    const index = e.currentTarget.dataset.index;
    const productInfo = this.data.currentProducts[index];

    console.error(
      `图片加载失败，索引: ${index}，URL: ${productInfo.icon}，产品名称: ${productInfo.name}`
    );

    // 更新数据，使用默认图片
    const defaultIcon = "/images/placeholder/tool-icon.png";
    const newProducts = [...this.data.currentProducts];
    newProducts[index].icon = defaultIcon;

    this.setData({
      currentProducts: newProducts,
    });

    // 显示轻提示，便于调试
    wx.showToast({
      title: `图片加载失败: ${productInfo.name}`,
      icon: "none",
      duration: 1500,
    });
  },

  // 确保标签在可视区域内，如果不在则滚动使其可见
  ensureTabVisible(index) {
    const tabId = `tab-${index}`; // 使用 index 构建 ID
    const padding = 10; // 滚动到可见区域时的边距 (px)

    wx.nextTick(() => {
      const query = wx.createSelectorQuery().in(this);
      query
        .select(".tab-scroll")
        .fields({ rect: true, scrollOffset: true, size: true }); // 获取滚动视图信息
      query.select(`#${tabId}`).boundingClientRect(); // 获取目标标签信息
      query.exec((res) => {
        if (res && res.length >= 2 && res[0] && res[1]) {
          const scrollViewRect = res[0];
          const tabItemRect = res[1];
          const currentScrollLeft = scrollViewRect.scrollLeft;
          const maxScrollLeft =
            scrollViewRect.scrollWidth - scrollViewRect.width;

          // 判断目标标签是否完全可见 (考虑padding)
          const isTabVisible =
            tabItemRect.left >= padding &&
            tabItemRect.left + tabItemRect.width <=
              scrollViewRect.width - padding;

          console.log(
            `[ensureTabVisible] Index: ${index}, Tab ID: ${tabId}, Is Visible: ${isTabVisible}, Tab Left: ${tabItemRect.left}, Tab Width: ${tabItemRect.width}, ScrollView Width: ${scrollViewRect.width}, ScrollView ScrollWidth: ${scrollViewRect.scrollWidth}`
          );

          // 如果标签已经完全可见，则不滚动
          if (isTabVisible) {
            console.log(
              "[ensureTabVisible] Tab already visible, skipping scroll."
            );
            return;
          }

          // --- 如果标签不可见，计算使其可见的滚动距离 ---
          let targetScrollLeft = currentScrollLeft; // 默认为当前位置

          // 情况1：标签在左侧不可见
          if (tabItemRect.left < padding) {
            // 增加一个额外的左侧偏移量，让标签不紧贴左边界
            const leftOffset = 20; // 增加20px的左侧偏移
            targetScrollLeft =
              currentScrollLeft + tabItemRect.left - padding - leftOffset;
            console.log(
              `[ensureTabVisible] Tab is off-screen left. Calculating scroll with offset...`
            );
          }
          // 情况2：标签在右侧不可见
          else if (
            tabItemRect.left + tabItemRect.width >
            scrollViewRect.width - padding
          ) {
            targetScrollLeft =
              currentScrollLeft +
              (tabItemRect.left + tabItemRect.width - scrollViewRect.width) +
              padding;
            console.log(
              `[ensureTabVisible] Tab is off-screen right. Calculating scroll...`
            );
          }

          // 边界检查
          targetScrollLeft = Math.max(0, targetScrollLeft);
          targetScrollLeft = Math.min(targetScrollLeft, maxScrollLeft);

          console.log(
            `[ensureTabVisible] Calculated scroll: current=${currentScrollLeft}, target=${targetScrollLeft}, max=${maxScrollLeft}`
          );

          // 只有在计算出的滚动位置和当前位置显著不同时才更新
          if (
            isFinite(targetScrollLeft) &&
            Math.abs(targetScrollLeft - currentScrollLeft) > 1
          ) {
            this.setData({ tabScrollLeft: targetScrollLeft });
            console.log(
              `[ensureTabVisible] Setting tabScrollLeft to ${targetScrollLeft}`
            );
          } else if (isFinite(targetScrollLeft)) {
            console.log(
              `[ensureTabVisible] Scroll difference too small or target is same as current. Skipping setData.`
            );
          }
        } else {
          console.error("[ensureTabVisible] Failed to get rect info", res);
        }
      });
    });
  },
});

<!--pages/index/index.wxml-->
<!-- 页面主容器 -->
<view class="container">
  <!-- 顶部导航说明 -->
  <!-- <view class="nav-intro">
    <text class="nav-intro-text">本应用仅提供AI工具信息导航，不直接提供AI服务</text>
  </view> -->

  <!-- 顶部可滑动标签栏容器 -->
  <view class="tab-scroll-container">
    <!-- 可横向滚动的视图区域，用于承载分类标签 -->
    <scroll-view 
      class="tab-scroll" 
      scroll-x="true" 
      enable-flex="true" 
      scroll-left="{{tabScrollLeft}}" 
      show-scrollbar="{{false}}" 
      enhanced="{{true}}" 
      bounces="{{false}}">
      <!-- 循环渲染分类标签 -->
      <!-- tab项的基础样式类，并根据当前选中的tab (currentTab) 与当前项的索引 (index) 是否相等来动态添加 'active' 类 -->
      <!-- 使用 wx:for 遍历 categories 数组，为每个分类创建一个tab项 -->
      <!-- 指定循环项的唯一标识符为 item.id，优化渲染性能 -->
      <!-- 设置每个tab项的DOM ID，格式为 "tab-" 加上索引，用于 JS 查询 -->
      <!-- 将当前项的分类ID (item.id) 存储在 data-id 自定义属性中，方便在事件处理函数中获取 -->
      <!-- 将当前项在循环中的索引 (index) 存储在 data-index 自定义属性中 -->
      <!-- 绑定点击事件，当用户点击tab时，调用页面JS文件中的 switchTab 方法 -->
      <view 
        class="tab-item {{currentTab === index ? 'active' : ''}}" 
        wx:for="{{categories}}" 
        wx:key="id"
        id="tab-{{index}}"
        data-id="{{item.id}}"
        data-index="{{index}}"
        bindtap="switchTab">
        <!-- 显示分类的名称 -->
        <text>{{item.name}}</text>
      </view>
    </scroll-view>
  </view>
  
  <!-- 使用swiper组件实现左右滑动效果 -->
  <swiper 
    class="category-swiper" 
    current="{{currentTab}}" 
    bindchange="handleSwiperChange"
    duration="300"
    circular="{{false}}"
    easing-function="easeInOutCubic"
    indicator-dots="{{false}}"
    previous-margin="0"
    next-margin="0"
    display-multiple-items="1">
    
    <!-- 手势引导提示 (仅在首次加载时显示) -->
    <view class="swipe-gesture-hint" wx:if="{{showSwipeHint}}">
      <view class="hint-icon"></view>
      <view class="hint-text">左右滑动查看更多分类</view>
    </view>
    
    <!-- 为每个分类创建一个swiper-item -->
    <swiper-item 
      wx:for="{{categories}}" 
      wx:key="id" 
      class="swiper-item {{currentTab === index ? 'swiper-item-active' : ''}}">
      
      <!-- 产品列表容器 -->
      <scroll-view 
        scroll-y="true" 
        class="products-scroll"
        wx:if="{{index >= currentTab - 1 && index <= currentTab + 1}}">
        
        <!-- 产品列表 -->
        <view class="products-list">
          <view 
            wx:for="{{currentTab === index ? currentProducts : (categoryProducts[categories[index].id] || [])}}" 
            wx:for-index="productIndex"
            wx:for-item="product"
            wx:key="productIndex"
            class="product-item"
            data-index="{{productIndex}}"
            bindtap="goToDetail">
            
            <!-- 显示产品图标 -->
            <image 
              class="product-icon" 
              src="{{product.icon || '/images/placeholder/tool-icon.png'}}" 
              mode="aspectFill"
              lazy-load="true"
              binderror="handleImageError"
              data-index="{{productIndex}}">
            </image>
            
            <!-- 添加备用图片，默认隐藏，图片加载错误时显示 -->
            <image 
              class="product-icon default-icon" 
              src="/images/placeholder/tool-icon.png" 
              mode="aspectFill"
              wx:if="{{product.icon.indexOf('cloud://') === 0}}"
              style="display: none;">
            </image>
            
            <!-- 显示产品名称 -->
            <view class="product-name">{{product.name}}</view>
          </view>
        </view>
      </scroll-view>
    </swiper-item>
  </swiper>
</view>
/* index.wxss */
/* 设置页面的全局样式 */
page {
  background-color: #f6f6f6;
  /* 设置页面背景颜色为浅灰色 */
  height: 100%;
  /* 设置页面高度为视口高度的100% */
}

/* 主容器样式 */
.container {
  display: flex;
  /* 使用 Flexbox 布局 */
  flex-direction: column;
  /* 设置主轴方向为垂直方向 */
  height: 100%;
  /* 设置容器高度为父元素（page）的100% */
  box-sizing: border-box;
  /* 设置盒模型为 border-box，padding 和 border 不会增加元素总宽高 */
  padding: 0 0 30rpx 0;
  /* 设置容器的内边距，底部留出 30rpx */
}

/* 顶部可滑动标签栏容器样式 */
.tab-scroll-container {
  width: 100%;
  /* 容器宽度占满父元素 */
  height: 88rpx;
  /* 设置容器固定高度 */
  background-color: #ffffff;
  /* 设置背景颜色为白色 */
  position: sticky;
  /* 设置为粘性定位，使其在滚动时固定在顶部 */
  top: 0;
  /* 固定在距离视口顶部 0 的位置 */
  z-index: 100;
  /* 设置层叠顺序，确保在其他内容之上 */
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  /* 添加底部阴影效果 */
}

/* 可滑动标签栏本身样式 */
.tab-scroll {
  width: 100%;
  /* 宽度占满父容器 */
  height: 88rpx;
  /* 高度与父容器一致 */
  white-space: nowrap;
  /* 强制内部元素不换行，以便横向滚动 */
  /* 隐藏标准滚动条 (Firefox) */
  scrollbar-width: none;
  /* 隐藏标准滚动条 (IE and Edge) */
  -ms-overflow-style: none;
  overflow-x: auto;
  /* 允许内容在水平方向上滚动 */
  overflow-y: hidden;
  /* 禁止内容在垂直方向上滚动 */
  padding: 0 0 0 0rpx;
  /* 减少左侧内边距，让标签更靠左显示 */
}

/* 隐藏 WebKit 内核浏览器（如 Chrome, Safari）的滚动条 */
.tab-scroll::-webkit-scrollbar {
  display: none;
  /* 不显示滚动条 */
  width: 0;
  /* 设置滚动条宽度为0 */
  height: 0;
  /* 设置滚动条高度为0 */
}

/* 单个标签项样式 */
.tab-item {
  display: inline-block;
  /* 设置为行内块元素，使其可以在一行内排列并拥有宽高 */
  padding: 0 30rpx;
  /* 设置左右内边距，增加点击区域和视觉间距 */
  height: 88rpx;
  /* 设置高度与容器一致 */
  line-height: 88rpx;
  /* 设置行高与高度一致，实现文字垂直居中 */
  font-size: 26rpx;
  /* 设置字体大小 */
  color: #666;
  /* 设置默认文字颜色 */
  position: relative;
  /* 设置相对定位，为其伪元素定位提供基准 */
  transition: all 0.3s ease;
  /* 添加过渡效果，使变化更平滑 */
  margin: 0 2rpx;
  /* 添加轻微的左右外边距，防止缩放时挤压相邻元素 */
  transform-origin: center center;
  /* 修改变换原点为中心，减少抖动 */
}

/* 当前激活的标签项样式 */
.tab-item.active {
  color: #07c160;
  /* 设置激活状态下的文字颜色为绿色 */
  /* 移除字体加粗，减少文字宽度突变导致的抖动 */
  transform: scale(1.05);
  /* 添加轻微放大效果 */
  font-size: 26rpx;
  /* 保持字体大小一致 */
  text-shadow: 0 0 1px rgba(7, 193, 96, 0.2);
  /* 添加微妙的文字阴影，增强视觉效果 */
  transform-origin: center center;
  /* 变换原点为中心 */
}

/* 激活标签项下方的指示条样式 (使用伪元素 ::after) */
.tab-item.active::after {
  content: '';
  /* 伪元素必须设置 content 属性 */
  position: absolute;
  /* 设置绝对定位，相对于 .tab-item */
  bottom: 0;
  /* 定位在父元素底部 */
  left: 50%;
  /* 水平居中定位的起点 */
  transform: translateX(-50%);
  /* 通过 transform 实现精确水平居中 */
  width: 40rpx;
  /* 设置指示条宽度 */
  height: 6rpx;
  /* 设置指示条高度 */
  background-color: #07c160;
  /* 设置指示条背景颜色与激活文字颜色一致 */
  border-radius: 3rpx;
  /* 设置圆角 */
}

/* 滑动视图容器样式 */
.category-swiper {
  flex: 1;
  width: 100%;
  height: 100%;
  transition: all 0.3s ease;
}

/* 滑动视图项样式 */
.swiper-item {
  height: 100%;
  box-sizing: border-box;
  transition: transform 0.3s ease;
}

/* 激活中的swiper-item添加淡入效果 */
.swiper-item-active {
  animation: fadeIn 0.3s ease forwards;
}

/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0.8;
  }

  to {
    opacity: 1;
  }
}


/* 产品列表滚动区域样式 */
.products-scroll {
  height: 100%;
  box-sizing: border-box;
  padding: 20rpx;
}

/* 产品列表布局样式 (Flex 换行布局) */
.products-list {
  display: flex;
  /* 使用 Flexbox 布局 */
  flex-direction: row;
  /* 主轴方向为水平 */
  flex-wrap: wrap;
  /* 允许项目换行 */
  width: 100%;
  /* 宽度占满父容器 */
  justify-content: space-between;
  /* 使用负 margin 来抵消子元素 .product-item 的左右 margin，使得整体内容靠边 */
}

/* 单个产品项卡片样式 */
.product-item {
  width: calc(50% - 10rpx);
  /* 计算宽度，实现一行显示两个，减去左右 margin 的总和 (10rpx * 2) */
  /* 如果需要一行显示三个，可以使用 calc(33.333% - 20rpx) */
  margin: 10rpx 0;
  /* 设置外边距，提供卡片间的间距 */
  padding: 16rpx;
  /* 设置内边距 */
  background-color: #ffffff;
  /* 设置背景颜色为白色 */
  border-radius: 12rpx;
  /* 设置圆角 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  /* 添加轻微阴影效果 */
  display: flex;
  /* 使用 Flexbox 布局 */
  flex-direction: column;
  /* 设置主轴方向为垂直 */
  align-items: center;
  /* 交叉轴（水平）居中对齐 */
  box-sizing: border-box;
  /* 盒模型设置为 border-box */
  transition: all 0.3s ease;
  /* 为所有可过渡属性添加 0.3 秒的缓动效果 */
}

/* 产品项被点击时的交互样式 */
.product-item:active {
  transform: scale(0.98);
  /* 轻微缩小 */
  box-shadow: 0 0 4rpx rgba(0, 0, 0, 0.1);
  /* 阴影变淡变小 */
}

/* 产品图标样式 */
.product-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background-color: #f7f7f7;
  /* 添加背景色，图片加载前显示 */
}

.default-icon {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  /* 默认在底层 */
}

/* 当主图片加载失败时，显示默认图标 */
.product-icon[src='']+.default-icon,
.product-icon[src='/images/placeholder/tool-icon.png']+.default-icon {
  z-index: 1;
  /* 提升到上层 */
  display: block !important;
}

/* 产品名称样式 */
.product-name {
  font-size: 24rpx;
  /* 设置字体大小 */
  color: #333;
  /* 设置文字颜色 */
  text-align: center;
  /* 文字居中对齐 */
  width: 100%;
  /* 宽度占满父容器 */
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  white-space: nowrap;
  /* 强制文本不换行 */
}

/* 产品描述样式 (当前 WXML 中未使用，但保留样式) */
.product-desc {
  font-size: 24rpx;
  /* 设置字体大小 */
  color: #666;
  /* 设置文字颜色 */
  margin-top: 8rpx;
  /* 设置与上方元素的间距 */
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  white-space: nowrap;
  /* 强制文本不换行 */
}

/* 详情按钮样式 (当前 WXML 中未使用，但保留样式) */
.detail-btn {
  font-size: 24rpx;
  /* 设置字体大小 */
  color: white;
  /* 设置文字颜色为白色 */
  background-color: #07c160;
  /* 设置背景颜色为绿色 */
  border-radius: 8rpx;
  /* 设置圆角 */
  width: 180rpx;
  /* 设置固定宽度 */
  height: 60rpx;
  /* 设置固定高度 */
  line-height: 60rpx;
  /* 设置行高与高度一致，实现文字垂直居中 */
  margin: 0;
  /* 清除默认外边距 */
  padding: 0;
  /* 清除默认内边距 */
  margin-top: 16rpx;
  /* 设置与上方元素的间距 */
}

/* 移除按钮默认的边框样式 (针对微信小程序 button 组件的 ::after 伪元素) */
.detail-btn::after {
  border: none;
  /* 不显示边框 */
}

/* 调试信息容器样式 */
.debug-info {
  display: none;
  /* 默认隐藏调试信息 */
  /* 如果需要显示调试信息，可以改为 display: block; */
}

/* 调试信息内部文本样式 */
.debug-info text {
  display: block;
  /* 设置为块级元素，使其各占一行 */
  margin: 6rpx 0;
  /* 设置上下外边距 */
}

/* 导航说明样式 */
.nav-intro {
  width: 100%;
  padding: 16rpx 30rpx;
  background-color: #fffde7;
  text-align: center;
  box-sizing: border-box;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.nav-intro-text {
  font-size: 24rpx;
  color: #9e9d24;
  line-height: 1.5;
}

/* 移除滑动指示器样式 */
.swipe-indicator {
  display: none;
}

/* 产品列表区域容器样式 */
.products-container {
  flex: 1;
  width: 100%;
  box-sizing: border-box;
}

/* 滑动手势提示 */
.swipe-gesture-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 20rpx;
  padding: 20rpx 30rpx;
  animation: fadeInOut 3s ease-in-out forwards;
}

.hint-icon {
  width: 80rpx;
  height: 30rpx;
  margin-bottom: 10rpx;
  position: relative;
  overflow: hidden;
}

.hint-icon::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  background-color: white;
  animation: moveLeftRight 2s infinite ease-in-out;
}

.hint-text {
  color: white;
  font-size: 24rpx;
}

@keyframes moveLeftRight {
  0% {
    left: 0;
  }

  50% {
    left: calc(100% - 30rpx);
  }

  100% {
    left: 0;
  }
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
  }

  20% {
    opacity: 1;
  }

  80% {
    opacity: 1;
  }

  100% {
    opacity: 0;
    display: none;
  }
}

/* 隐藏产品列表滚动区域的滚动条 (WebKit) */
.products-scroll::-webkit-scrollbar {
  display: none;
  /* 恢复原状 */
  width: 0;
  /* 恢复原状 */
  height: 0;
  /* 恢复原状 */
  color: transparent;
}
// 使用数据管理器工厂获取工具数据
const dataManagerFactory = require("../../utils/dataManagerFactory");
const toolsDataManager = dataManagerFactory.getDataManager();
const config = require("../../config/config");

Page({
  data: {
    tool: null,
    loading: true,
    defaultIcon: "/images/placeholder/tool-icon.png",
    dataSourceType: "", // 当前使用的数据源类型
  },

  onLoad(options) {
    // 从 options 中获取编码后的 id
    const encodedId = options.id;
    // 对 id 进行解码
    const id = decodeURIComponent(encodedId);

    // 设置数据源类型
    const dataSourceType = dataManagerFactory.getDataSourceType();
    this.setData({ dataSourceType });
    console.log(`工具详情页数据源类型: ${dataSourceType}`);

    console.log("[ToolDetail] onLoad: 收到的原始 id:", encodedId);
    console.log("[ToolDetail] onLoad: 解码后的 id:", id);

    // 使用解码后的 id 获取工具详情
    this.getToolById(id);
  },

  getToolById(id) {
    this.setData({
      loading: true,
      tool: null,
    });

    toolsDataManager
      .getToolById(id)
      .then((fetchedTool) => {
        if (!fetchedTool) {
          wx.showToast({ title: "未找到对应工具", icon: "none" });
          this.setData({ loading: false });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
          return;
        }

        // 根据数据源类型处理图片路径
        if (this.data.dataSourceType === "cloud") {
          // 云数据源处理方式
          this.processCloudImage(fetchedTool, id);
        } else {
          // 本地数据源直接使用
          console.log(
            `[ToolDetail] 使用本地数据源，工具 ID: ${id}。图标路径: ${fetchedTool.icon}`
          );
          this.setData({
            tool: fetchedTool,
            loading: false,
          });
        }
      })
      .catch((error) => {
        console.error("获取工具详情失败:", error);
        wx.showToast({ title: "获取工具详情失败", icon: "none" });
        this.setData({ loading: false });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      });
  },

  // 处理云存储图片
  processCloudImage(fetchedTool, id) {
    const cacheKey = `icon_cache_${id}`;
    const cachedIconPath = wx.getStorageSync(cacheKey);

    // 1. 检查缓存
    if (cachedIconPath) {
      console.log(
        `[工具详情] 图标缓存命中，工具 ID: ${id}。使用本地路径: ${cachedIconPath}`
      );
      fetchedTool.icon = cachedIconPath;
      this.setData({ tool: fetchedTool, loading: false });
      // 2. 无缓存，且是云存储路径，则获取临时链接并尝试缓存
    } else if (fetchedTool.icon && fetchedTool.icon.startsWith("cloud://")) {
      console.log(
        `[ToolDetail] No cache found for ${id}. Fetching temp URL for ${fetchedTool.icon}`
      );
      const originalCloudPath = fetchedTool.icon; // 保留原始路径

      this.getCloudImageTempUrl(originalCloudPath)
        .then((tempUrl) => {
          if (tempUrl) {
            console.log(`[ToolDetail] Got temp URL for ${id}: ${tempUrl}`);
            // 使用临时链接进行本次显示
            fetchedTool.icon = tempUrl;
            this.setData({ tool: fetchedTool, loading: false });
            // 异步缓存图标
            this.cacheIconAsync(id, tempUrl);
          } else {
            // 云函数调用成功但未返回有效 URL
            console.error(
              `[ToolDetail] getCloudImageTempUrl returned null for ${id}`
            );
            fetchedTool.icon = this.data.defaultIcon;
            this.setData({ tool: fetchedTool, loading: false });
          }
        })
        .catch((err) => {
          // 云函数调用失败
          console.error(`[ToolDetail] Failed to get temp URL for ${id}:`, err);
          fetchedTool.icon = this.data.defaultIcon;
          this.setData({ tool: fetchedTool, loading: false });
        });
      // 3. 非云存储路径或无图标路径，使用原始或默认图标
    } else {
      console.log(
        `[ToolDetail] Icon for ${id} is not cloud path or missing. Using default/original.`
      );
      fetchedTool.icon = fetchedTool.icon || this.data.defaultIcon;
      this.setData({ tool: fetchedTool, loading: false });
    }
  },

  // 新增：异步缓存图标到本地存储
  cacheIconAsync(id, tempUrl) {
    try {
      const cacheKey = `tool_icon_${id}`;
      // 设置缓存，可以考虑添加过期时间
      wx.setStorageSync(cacheKey, tempUrl);
      console.log(
        `[ToolDetail] Icon for ${id} cached successfully: ${cacheKey}`
      );
    } catch (e) {
      console.error(`[ToolDetail] Failed to cache icon for ${id}:`, e);
    }
  },

  // 获取云存储图片的临时URL (修改为调用云函数)
  getCloudImageTempUrl(fileId) {
    return new Promise((resolve, reject) => {
      // 移除修正 File ID 的逻辑
      const correctedFileId = fileId; // 直接使用传入的 fileId

      // 使用 fileId 调用云函数
      console.log(
        `[ToolDetail] 开始通过云函数获取图片临时 URL: ${correctedFileId}`
      );
      wx.cloud.callFunction({
        name: "getJsonData", // 调用与首页相同的云函数
        data: {
          operation: "getIcons", // 指定操作为获取图标
          iconPaths: [correctedFileId], // 将修正后的 fileId 包装成数组传递
        },
        success: (res) => {
          console.log("[ToolDetail] 云函数获取图片临时URL成功:", res);

          if (
            res.result &&
            res.result.success &&
            res.result.data &&
            res.result.data.length > 0
          ) {
            const fileInfo = res.result.data[0];
            // 注意：这里比较时也应该用修正后的 ID
            if (
              fileInfo.fileID === correctedFileId && // 使用修正后的 ID 比较
              fileInfo.tempFileURL &&
              fileInfo.status === 0
            ) {
              console.log(
                `[ToolDetail] 成功解析到临时 URL: ${fileInfo.tempFileURL}`
              );
              resolve(fileInfo.tempFileURL);
            } else {
              // 日志中打印原始 fileId 和 修正后的 fileId 可能更有帮助
              console.error(
                `[ToolDetail] 云函数返回数据解析失败或状态错误: status=${fileInfo.status}, requestedFileID=${correctedFileId}, returnedFileID=${fileInfo.fileID}`
              );
              resolve(null); // 解析失败，返回 null
            }
          } else {
            console.error("[ToolDetail] 云函数返回数据格式错误:", res.result);
            resolve(null); // 格式错误，返回 null
          }
        },
        fail: (err) => {
          console.error("[ToolDetail] 调用云函数 getIcons 失败:", err);
          reject(err); // 云函数调用失败，reject Promise
        },
      });
    });
  },

  // 复制链接
  copyLink() {
    const { url } = this.data.tool;
    wx.setClipboardData({
      data: url,
      success: () => {
        wx.showToast({
          title: "链接已复制",
          icon: "success",
        });
      },
    });
  },

  // 跳转到工具网站
  openWebview() {
    const { url } = this.data.tool;
    wx.navigateTo({
      url: `/pages/web/index?url=${encodeURIComponent(url)}`,
    });
  },

  // 修改：处理图片加载错误
  handleImageError() {
    if (this.data.tool) {
      const currentIcon = this.data.tool.icon;
      const defaultIcon = this.data.defaultIcon;
      const toolId = this.data.tool.id;

      console.error(
        `[ToolDetail] Image load failed for tool ${toolId}. Failed URL: ${currentIcon}`
      );

      // 设置默认图标（如果当前不是默认图标）
      if (currentIcon !== defaultIcon) {
        console.log(`[ToolDetail] Setting default icon for ${toolId}`);
        this.setData({
          "tool.icon": defaultIcon,
        });
      } else {
        console.warn(
          `[ToolDetail] Default icon itself failed to load or was already set: ${defaultIcon}`
        );
      }
    } else {
      console.error(
        "[ToolDetail] handleImageError called but tool data is missing."
      );
    }
  },
});

<!-- pages/tool-detail/index.wxml -->
<view class="container">
  <block wx:if="{{loading}}">
    <view class="loading">
      <text>加载中...</text>
    </view>
  </block>
  <block wx:else>
    <!-- 顶部卡片 -->
    <view class="hero-section">
      <view class="tool-header">
        <view class="tool-icon">
          <image 
            src="{{tool.icon || '/images/placeholder/tool-icon.png'}}" 
            mode="aspectFill"
            lazy-load="true"
            binderror="handleImageError"></image>
        </view>
        <view class="tool-info">
          <view class="tool-name-row">
            <view class="tool-name">{{tool.name}}</view>
          </view>
          <!-- 使用tags字段显示标签 -->
          <view class="tool-labels" wx:if="{{tool.tags && tool.tags.length > 0}}">
            <view class="tool-tag" wx:for="{{tool.tags}}" wx:key="index">{{item}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 工具详情 -->
    <view class="detail-card">
      <view class="card-title">工具详情</view>
      
      <!-- 工具描述 -->
      <view class="detail-section">
        <view class="section-title">
          <text class="icon">📝</text>
          <text>工具描述</text>
        </view>
        <view class="section-content">
          <view class="tool-desc">{{tool.description}}</view>
        </view>
      </view>
      
      <!-- 访问地址 -->
      <view class="detail-section">
        <view class="section-title">
          <text class="icon">🔗</text>
          <text>访问地址</text>
        </view>
        <view class="section-content url-box">
          <text class="url">{{tool.url}}</text>
          <view class="copy-btn" bindtap="copyLink">复制</view>
        </view>
      </view>
    </view>

    <!-- 使用说明 -->
    <view class="detail-card">
      <view class="card-title">使用说明</view>
      <view class="usage-steps">
        <view class="step">
          <view class="step-number">1</view>
          <view class="step-content">点击"复制链接"按钮，并在浏览器中访问工具网站</view>
        </view>
        <view class="step">
          <view class="step-number">2</view>
          <view class="step-content">根据网站指引完成注册/登录（如需要）</view>
        </view>
        <view class="step">
          <view class="step-number">3</view>
          <view class="step-content">按照工具提示使用AI功能</view>
        </view>
      </view>
    </view>


  </block>
</view> 
/* pages/tool-detail/index.wxss */
.container {
    padding: 30rpx;
    background-color: #f7f8fa;
    min-height: 100vh;
    box-sizing: border-box;
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300rpx;
    color: #999;
    font-size: 28rpx;
}

/* 卡片通用样式 */
.hero-section,
.detail-card {
    background-color: #fff;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    margin-bottom: 30rpx;
    width: 100%;
    box-sizing: border-box;
}

/* 顶部卡片 */
.tool-header {
    display: flex;
    padding: 40rpx 30rpx;
}

.tool-icon {
    width: 120rpx;
    height: 120rpx;
    border-radius: 24rpx;
    overflow: hidden;
    margin-right: 30rpx;
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
    background-color: #f0f0f0;
}

.tool-icon image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.tool-info {
    flex: 1;
}

.tool-name-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16rpx;
}

.tool-name {
    font-size: 40rpx;
    font-weight: bold;
    color: #333;
}

/* 标签样式 */
.tool-labels {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 16rpx;
}

.tool-tag {
    font-size: 24rpx;
    padding: 6rpx 18rpx;
    border-radius: 12rpx;
    margin-right: 16rpx;
    margin-bottom: 8rpx;
    color: #555;
    background-color: #e6f2ff;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

/* 为不同标签设置不同颜色 */
.tool-tag:nth-child(1) {
    background-color: #e6f2ff;
    /* 浅蓝色 */
    color: #4080c0;
}

.tool-tag:nth-child(2) {
    background-color: #fff2e6;
    /* 浅橙色 */
    color: #e67300;
}

.tool-tag:nth-child(3) {
    background-color: #e6ffe6;
    /* 浅绿色 */
    color: #2d862d;
}

.tool-tag:nth-child(4) {
    background-color: #f2e6ff;
    /* 浅紫色 */
    color: #8c4db8;
}

.tool-tag:nth-child(5) {
    background-color: #ffe6e6;
    /* 浅红色 */
    color: #cc3333;
}

/* 原有样式保持不变 */
.tool-category.hot {
    background-color: #ffebee;
    color: #f44336;
}

.tool-category.search {
    background-color: #e3f2fd;
    color: #2196f3;
}

.tool-category.text {
    background-color: #e8f5e9;
    color: #4caf50;
}

.tool-category.image {
    background-color: #ede7f6;
    color: #673ab7;
}

.tool-label {
    font-size: 24rpx;
    padding: 4rpx 16rpx;
    border-radius: 12rpx;
}

.tool-label.hot {
    background-color: #fff8e1;
    color: #ffc107;
}

.tool-label.recommend {
    background-color: #e8f5e9;
    color: #4caf50;
}

.tool-desc {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
}

/* 卡片标题统一样式 */
.card-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
    padding: 30rpx 30rpx 20rpx 30rpx;
    border-bottom: 1px solid #f0f0f0;
}

/* 详情部分内容padding统一 */
.detail-section {
    margin-bottom: 30rpx;
    padding-left: 30rpx;
    padding-right: 30rpx;
}

.detail-section:last-child {
    margin-bottom: 0;
    padding-bottom: 30rpx;
}

.section-title {
    display: flex;
    align-items: center;
    font-size: 30rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 16rpx;
}

.section-title .icon {
    margin-right: 12rpx;
    font-size: 32rpx;
}

.section-content {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
}

/* URL 样式 */
.url-box {
    display: flex;
    align-items: center;
    background-color: #f7f8fa;
    padding: 20rpx;
    border-radius: 12rpx;
}

.url {
    flex: 1;
    word-break: break-all;
    color: #2196f3;
}

.copy-btn {
    margin-left: 20rpx;
    padding: 8rpx 20rpx;
    background-color: #2196f3;
    color: #fff;
    border-radius: 30rpx;
    font-size: 24rpx;
}

/* 适用场景标签 */
.scene-list {
    display: flex;
    flex-wrap: wrap;
}

.scene-tag {
    padding: 12rpx 24rpx;
    background-color: #f7f8fa;
    color: #666;
    border-radius: 30rpx;
    margin-right: 16rpx;
    margin-bottom: 16rpx;
    font-size: 26rpx;
}

/* 评分样式 */
.rating-container {
    display: flex;
    align-items: center;
}

.rating-stars {
    display: flex;
}

.star {
    font-size: 36rpx;
    color: #e0e0e0;
    margin-right: 6rpx;
}

.star.active {
    color: #ffc107;
}

.star.half-active {
    position: relative;
    color: #e0e0e0;
}

.star.half-active::after {
    content: '★';
    position: absolute;
    left: 0;
    top: 0;
    width: 50%;
    overflow: hidden;
    color: #ffc107;
}

.rating-text {
    margin-left: 16rpx;
    color: #ffc107;
    font-weight: bold;
}

/* 使用步骤 */
.usage-steps {
    padding: 10rpx 30rpx 30rpx 30rpx;
}

.step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 24rpx;
}

.step:last-child {
    margin-bottom: 0;
}

.step-number {
    width: 48rpx;
    height: 48rpx;
    border-radius: 24rpx;
    background-color: #2196f3;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    margin-right: 20rpx;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
    font-size: 28rpx;
    color: #666;
    padding-top: 6rpx;
}

/* 底部按钮 */
.bottom-actions {
    display: flex;
    position: sticky;
    bottom: 30rpx;
    z-index: 10;
}

.action-btn {
    flex: 1;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 15rpx;
}

.action-btn.copy {
    background-color: #f0f0f0;
    color: #666;
}

.action-btn.open {
    background-color: #2196f3;
    color: #fff;
    box-shadow: 0 4rpx 12rpx rgba(33, 150, 243, 0.3);
}

/* 免责声明样式 */
.disclaimer-card {
    border: 1px solid #e0e0e0;
    background-color: #fffde7;
}

.disclaimer-content {
    padding: 0 30rpx 30rpx 30rpx;
}

.disclaimer-content text {
    color: #757575;
    font-size: 26rpx;
    line-height: 1.6;
}
/**
 * 云函数数据管理器
 * 通过云函数从云存储获取AI工具数据
 */

// 引入配置
const config = require("../config/config");
const { DATA_VERSION } = config;
// 引入图片缓存管理器
const imageCacheManager = require("./imageCacheManager");

// 获取云环境ID
const app = getApp();
const ENV_ID = app ? app.getCloudEnvId() : config.DATA_SOURCE.CLOUD.ENV_ID;

// 内存中的数据缓存
let memoryCache = null;

// 数据版本日期
const DATA_VERSION_DATE = DATA_VERSION.DATE;

// JSON文件在云存储中的路径 - 使用基于版本日期的路径
const TOOLS_JSON_PATH = config.DATA_SOURCE.CLOUD.TOOLS_JSON_PATH;
const IMAGES_BASE_PATH = config.DATA_SOURCE.CLOUD.IMAGES_BASE_PATH;

console.log(`云函数数据管理器初始化，使用数据版本: ${DATA_VERSION_DATE}`);
console.log(`工具JSON路径: ${TOOLS_JSON_PATH}`);
console.log(`图片基础路径: ${IMAGES_BASE_PATH}`);

/**
 * 初始化工具数据
 * @returns {Promise<boolean>} 是否初始化成功
 */
function initToolsData() {
  console.log(
    `开始通过云函数加载JSON数据，版本: ${DATA_VERSION_DATE}, 环境ID: ${ENV_ID}...`
  );

  return new Promise((resolve, reject) => {
    if (memoryCache) {
      console.log("使用内存缓存数据");
      resolve(true);
      return;
    }

    fetchCloudData()
      .then((success) => {
        resolve(success);
      })
      .catch((error) => {
        console.error(
          `通过云函数获取JSON数据失败，版本: ${DATA_VERSION_DATE}:`,
          error
        );
        reject(error);
      });
  });
}

/**
 * 通过云函数从云存储获取JSON数据
 * @returns {Promise<boolean>} 是否获取成功
 */
async function fetchCloudData() {
  try {
    console.log(`正在调用云函数获取JSON数据，版本: ${DATA_VERSION_DATE}...`);

    // 调用云函数获取JSON数据
    const result = await wx.cloud.callFunction({
      name: "getJsonData",
      data: {
        filePath: TOOLS_JSON_PATH,
      },
    });

    console.log("🔍 云函数完整返回结果:", JSON.stringify(result, null, 2));

    if (!result || !result.result) {
      console.error(`❌ 云函数返回结果无效，版本: ${DATA_VERSION_DATE}`);
      console.error("完整result:", result);
      return false;
    }

    if (!result.result.success) {
      console.error(`❌ 云函数返回错误，版本: ${DATA_VERSION_DATE}`);
      console.error("错误信息:", result.result.error || "未提供错误信息");
      console.error("调试数据:", result.result.debug || "无调试数据");
      console.error(
        "完整result.result:",
        JSON.stringify(result.result, null, 2)
      );
      return false;
    }

    const data = result.result.data;
    console.log(`✅ 成功获取JSON数据，版本: ${DATA_VERSION_DATE}`);

    if (
      !data.categories ||
      !data.tools ||
      !Array.isArray(data.categories) ||
      !Array.isArray(data.tools)
    ) {
      console.error("❌ JSON数据格式不正确");
      console.error("数据结构:", {
        hasCategories: !!data.categories,
        categoriesIsArray: Array.isArray(data.categories),
        categoriesLength: data.categories?.length,
        hasTools: !!data.tools,
        toolsIsArray: Array.isArray(data.tools),
        toolsLength: data.tools?.length,
      });
      return false;
    }

    // 处理数据为所需格式
    const processedData = await processData(data.categories, data.tools);

    if (!processedData) {
      console.error("❌ 数据处理失败");
      return false;
    }

    // 更新内存缓存
    memoryCache = processedData;
    console.log(`✅ 数据加载完成，版本: ${DATA_VERSION_DATE}`);

    return true;
  } catch (error) {
    console.error(
      `❌ 调用云函数获取数据失败，版本: ${DATA_VERSION_DATE}:`,
      error
    );
    console.error("错误详情:", {
      name: error.name,
      message: error.message,
      stack: error.stack,
    });
    return false;
  }
}

/**
 * 处理数据为所需格式，并集成图片缓存
 * @param {Array} categories - 分类数据
 * @param {Array} tools - 工具数据
 * @returns {Object} 处理后的数据
 */
async function processData(categories, tools) {
  try {
    if (!Array.isArray(categories) || !Array.isArray(tools)) {
      console.error("数据格式不正确");
      return null;
    }

    console.log(`[CloudManager] 开始处理数据，工具数量: ${tools.length}`);

    // 第一步：处理云存储路径格式，将旧路径转换为新版本路径
    const processedTools = tools.map((tool) => {
      if (tool.icon && tool.icon.indexOf("cloud://") === 0) {
        // 检查是否是旧的 ai_tools/images 路径格式
        if (tool.icon.indexOf("/ai_tools/images/") !== -1) {
          // 将 ai_tools/images 替换为当前版本的数据路径
          const newIcon = tool.icon.replace(
            "/ai_tools/images/",
            `/${IMAGES_BASE_PATH}/`
          );
          console.log(`图标路径转换: ${tool.name}`);
          console.log(`  原路径: ${tool.icon}`);
          console.log(`  新路径: ${newIcon}`);
          tool.icon = newIcon;
        } else if (tool.icon.indexOf(`/数据${DATA_VERSION_DATE}/`) !== -1) {
          // 如果已经是正确的版本路径，保持不变
          console.log(`图标路径已正确: ${tool.name} -> ${tool.icon}`);
        } else {
          // 其他情况，尝试构建正确的路径
          const iconPathParts = tool.icon.split("/");
          const fileName = iconPathParts[iconPathParts.length - 1];
          const category = tool.categoryId || "未分类";

          const newIcon = `cloud://${ENV_ID}.636c-${ENV_ID}-1349397796/${IMAGES_BASE_PATH}/${category}/${fileName}`;
          console.log(`图标路径重建: ${tool.name}`);
          console.log(`  原路径: ${tool.icon}`);
          console.log(`  新路径: ${newIcon}`);
          tool.icon = newIcon;
        }
      } else if ((!tool.icon || tool.icon === "") && tool.localIcon) {
        // 处理icon为空但localIcon有值的情况
        const newIcon = `cloud://${ENV_ID}.636c-${ENV_ID}-1349397796/数据${DATA_VERSION_DATE}/${tool.localIcon}`;
        console.log(`从localIcon构建图标路径: ${tool.name}`);
        console.log(`  localIcon: ${tool.localIcon}`);
        console.log(`  新路径: ${newIcon}`);
        tool.icon = newIcon;
      } else if (!tool.icon || tool.icon === "") {
        // 如果既没有icon也没有localIcon，记录警告
        console.warn(`工具 ${tool.name} 缺少图标信息`);
      }
      return tool;
    });

    // 第二步：获取云存储图片的临时URL
    const toolsWithTempUrls = await getCloudImageTempUrls(processedTools);

    // 第三步：使用图片缓存管理器处理图片
    const finalTools = await processBatchImagesWithCache(toolsWithTempUrls);

    // 按分类组织工具数据
    const toolsData = {};

    categories.forEach((category) => {
      const categoryId = category.id;
      // 找出属于该分类的所有工具
      toolsData[categoryId] = finalTools.filter(
        (tool) => tool.categoryId === categoryId
      );
    });

    console.log(`数据处理完成，版本: ${DATA_VERSION_DATE}, 信息汇总:`);
    console.log("- 分类数量:", categories.length);
    console.log("- 总工具数量:", finalTools.length);

    return {
      categories,
      toolsData,
      allTools: finalTools,
    };
  } catch (error) {
    console.error("处理数据出错:", error);
    return null;
  }
}

/**
 * 获取云存储图片的临时访问URL
 * @param {Array} tools - 工具数据数组
 * @returns {Array} 处理后的工具数据
 */
async function getCloudImageTempUrls(tools) {
  console.log(`[CloudManager] 开始获取云存储图片临时URL`);

  // 获取所有需要处理的云文件ID
  const cloudPaths = tools
    .filter((tool) => tool.icon && tool.icon.indexOf("cloud://") === 0)
    .map((tool) => tool.icon);

  if (cloudPaths.length === 0) {
    console.log("没有需要处理的云存储图片");
    return tools;
  }

  console.log(`需要处理的云存储图片数量: ${cloudPaths.length}`);
  console.log("前3个图标路径示例:", cloudPaths.slice(0, 3));

  try {
    // 使用云函数获取临时访问URL
    console.log("正在调用云函数获取图标临时URL...");
    const result = await wx.cloud.callFunction({
      name: "getJsonData",
      data: {
        operation: "getIcons",
        iconPaths: cloudPaths,
      },
    });

    console.log("🔍 图标云函数返回结果:", JSON.stringify(result, null, 2));

    if (!result.result || !result.result.success || !result.result.data) {
      console.error("❌ 图标云函数返回错误:");
      console.error("- success:", result.result?.success);
      console.error("- error:", result.result?.error);
      console.error("- debug:", result.result?.debug);
      console.error("完整result:", result);

      // 为所有云存储图片设置默认图片
      return tools.map((tool) => {
        if (tool.icon && tool.icon.indexOf("cloud://") === 0) {
          console.log(`设置默认图标: ${tool.name}`);
          tool.icon = "/images/placeholder/tool-icon.png";
        }
        return tool;
      });
    }

    // 更新产品图标为临时URL
    const fileList = result.result.data || [];
    console.log(`✅ 图标云函数成功，返回${fileList.length}个文件结果`);

    // 创建fileID到tempFileURL的映射
    const fileIdToUrl = {};
    fileList.forEach((file, index) => {
      if (file.fileID && file.tempFileURL && file.status === 0) {
        fileIdToUrl[file.fileID] = file.tempFileURL;
        console.log(
          `图标[${index}] 成功: ${file.fileID} -> ${file.tempFileURL}`
        );
      } else if (file.status !== 0) {
        console.error(
          `图标[${index}] 失败: ${file.fileID}, 状态: ${file.status}, 错误: ${
            file.errMsg || "未知错误"
          }`
        );
      }
    });

    console.log(`有效图标URL映射数量: ${Object.keys(fileIdToUrl).length}`);

    // 更新工具的icon URL
    const processedTools = tools.map((tool) => {
      if (tool.icon && tool.icon.indexOf("cloud://") === 0) {
        const tempUrl = fileIdToUrl[tool.icon];
        if (tempUrl) {
          console.log(
            `✅ 成功获取临时URL: ${tool.name} -> ${tempUrl.substring(0, 80)}...`
          );
          tool.icon = tempUrl;
          tool.originalCloudPath = tool.icon; // 保存原始云路径
        } else {
          console.error(
            `❌ 无法获取临时URL: ${tool.name}, 文件ID: ${tool.icon}`
          );
          tool.icon = "/images/placeholder/tool-icon.png";
        }
      }
      return tool;
    });

    console.log(`[CloudManager] 临时URL获取完成`);
    return processedTools;
  } catch (error) {
    console.error("❌ 调用云函数获取图片临时URL失败:", error);
    console.error("错误详情:", {
      name: error.name,
      message: error.message,
      stack: error.stack,
    });

    // 为所有云存储图片设置默认图片
    return tools.map((tool) => {
      if (tool.icon && tool.icon.indexOf("cloud://") === 0) {
        tool.icon = "/images/placeholder/tool-icon.png";
      }
      return tool;
    });
  }
}

/**
 * 使用图片缓存管理器批量处理图片
 * @param {Array} tools - 工具数据数组
 * @returns {Array} 处理后的工具数据
 */
async function processBatchImagesWithCache(tools) {
  console.log(`[CloudManager] 开始使用缓存管理器处理图片`);

  try {
    // 批量处理所有图片URL
    const imageUrls = tools.map((tool) => tool.icon);
    const cachedImagePaths = await imageCacheManager.getBatchImages(imageUrls);

    // 更新工具数据中的图片路径
    const processedTools = tools.map((tool, index) => {
      const cachedPath = cachedImagePaths[index];
      if (cachedPath && cachedPath !== tool.icon) {
        console.log(
          `[CloudManager] 使用缓存图片: ${tool.name} -> ${cachedPath}`
        );
        tool.icon = cachedPath;
        tool.isCached = true;
      }
      return tool;
    });

    console.log(`[CloudManager] 图片缓存处理完成`);

    // 定期清理缓存大小
    setTimeout(() => {
      imageCacheManager.manageCacheSize();
    }, 1000);

    return processedTools;
  } catch (error) {
    console.error("[CloudManager] 批量处理图片缓存失败:", error);
    return tools;
  }
}

/**
 * 获取所有工具分类
 * @returns {Promise<Array>} 分类列表
 */
function getCategories() {
  console.log("获取分类数据...");

  return new Promise((resolve, reject) => {
    initToolsData()
      .then(() => {
        if (!memoryCache || !memoryCache.categories) {
          console.error("分类数据不可用，memoryCache:", memoryCache);
          reject(new Error("分类数据不可用"));
          return;
        }
        console.log("返回分类数据，数量:", memoryCache.categories.length);
        resolve(memoryCache.categories);
      })
      .catch(reject);
  });
}

/**
 * 根据分类ID获取工具列表
 * @param {string} categoryId - 分类ID
 * @returns {Promise<Array>} 工具列表
 */
function getToolsByCategory(categoryId) {
  console.log(`获取分类 ${categoryId} 的工具数据...`);

  return new Promise((resolve, reject) => {
    initToolsData()
      .then(() => {
        console.log("数据初始化完成，开始获取指定分类工具");
        const tools = getToolsByCategoryFromCache(categoryId);
        console.log(`返回分类 ${categoryId} 的工具数据，数量:`, tools.length);
        if (tools.length > 0) {
          console.log("工具示例:", tools[0]);
        }
        resolve(tools);
      })
      .catch((error) => {
        console.error(`获取分类 ${categoryId} 工具数据失败:`, error);
        reject(error);
      });
  });
}

/**
 * 从缓存中获取指定分类的工具列表
 * @param {string} categoryId - 分类ID
 * @returns {Array} 工具列表
 */
function getToolsByCategoryFromCache(categoryId) {
  if (!memoryCache || !memoryCache.toolsData) {
    console.error("工具数据不可用，memoryCache:", memoryCache);
    return [];
  }

  const tools = memoryCache.toolsData[categoryId] || [];
  console.log(`从缓存中获取分类 ${categoryId} 的工具，找到 ${tools.length} 个`);
  return tools;
}

/**
 * 获取所有工具数据
 * @returns {Promise<Array>} 所有工具列表
 */
function getAllToolsData() {
  console.log("获取所有工具数据...");

  return new Promise((resolve, reject) => {
    initToolsData()
      .then(() => {
        if (!memoryCache || !memoryCache.allTools) {
          console.error("工具数据不可用，memoryCache:", memoryCache);
          reject(new Error("工具数据不可用"));
          return;
        }
        console.log("返回所有工具数据，数量:", memoryCache.allTools.length);
        resolve(memoryCache.allTools);
      })
      .catch(reject);
  });
}

/**
 * 根据工具ID获取工具详情
 * @param {string} toolId - 工具ID
 * @returns {Promise<Object>} 工具详情
 */
function getToolById(toolId) {
  console.log(`获取工具 ${toolId} 的详情...`);

  return new Promise((resolve, reject) => {
    initToolsData()
      .then(() => {
        if (!memoryCache || !memoryCache.allTools) {
          console.error("工具数据不可用，memoryCache:", memoryCache);
          reject(new Error("工具数据不可用"));
          return;
        }

        // 根据工具ID查找工具详情
        const tool = memoryCache.allTools.find((item) => item.id === toolId);

        if (!tool) {
          console.error(`未找到ID为 ${toolId} 的工具`);
          reject(new Error(`未找到工具: ${toolId}`));
          return;
        }

        console.log(`返回工具 ${toolId} 的详情:`, tool);
        resolve(tool);
      })
      .catch(reject);
  });
}

/**
 * 获取图片缓存统计信息
 * @returns {Object} 缓存统计
 */
function getImageCacheStats() {
  return imageCacheManager.getCacheStats();
}

/**
 * 清理图片缓存
 * @returns {Promise} 清理结果
 */
function clearImageCache() {
  return imageCacheManager.clearAllCache();
}

// 导出模块接口
module.exports = {
  initToolsData,
  getCategories,
  getToolsByCategory,
  getAllToolsData,
  getToolById,
  getImageCacheStats,
  clearImageCache,
};

/**
 * 数据管理器工厂
 * 根据配置决定使用本地还是云端数据管理器
 */

const config = require("../config/config");
const cloudDataManager = require("./cloudFunctionManager");
const localDataManager = require("./localDataManager");

// 根据配置选择数据管理器
function getDataManager() {
  const dataSourceType = config.DATA_SOURCE.TYPE;

  console.log(
    `数据管理器工厂: 使用${dataSourceType === "local" ? "本地" : "云端"}数据源`
  );

  if (dataSourceType === "local") {
    return localDataManager;
  } else {
    return cloudDataManager;
  }
}

// 获取数据源类型
function getDataSourceType() {
  return config.DATA_SOURCE.TYPE;
}

// 获取当前环境ID
function getCloudEnvId() {
  return config.DATA_SOURCE.CLOUD.ENV_ID;
}

module.exports = {
  getDataManager,
  getDataSourceType,
  getCloudEnvId,
};

/**
 * 图片缓存管理器
 * 用于云端模式下的图片持久化缓存
 */

const config = require("../config/config");
const { IMAGE_CACHE } = config;

class ImageCacheManager {
  constructor() {
    this.fileSystemManager = wx.getFileSystemManager();
    this.cacheDir = `${wx.env.USER_DATA_PATH}/${IMAGE_CACHE.CACHE_DIR_NAME}`;
    this.metaDataKey = IMAGE_CACHE.METADATA_KEY;
    this.maxCacheSize = IMAGE_CACHE.MAX_CACHE_SIZE;
    this.cacheExpireDays = IMAGE_CACHE.CACHE_EXPIRE_DAYS;
    this.maxConcurrentDownloads = IMAGE_CACHE.MAX_CONCURRENT_DOWNLOADS;
    this.downloadQueue = []; // 下载队列
    this.activeDownloads = 0; // 当前活跃下载数
    this.enabled = IMAGE_CACHE.ENABLED;
    this.targetSizeRatio = IMAGE_CACHE.CLEANUP.TARGET_SIZE_RATIO;
    this.placeholderImage = IMAGE_CACHE.PLACEHOLDER_IMAGE;

    if (this.enabled) {
      this.init();
    }
  }

  /**
   * 初始化缓存目录和元数据
   */
  init() {
    try {
      // 确保缓存目录存在
      this.fileSystemManager.mkdirSync(this.cacheDir, true);
      console.log("[ImageCache] 缓存目录初始化成功");
    } catch (error) {
      // 改进错误识别逻辑，匹配多种可能的错误信息格式
      const errorMsg = error.errMsg || error.message || String(error);
      if (
        errorMsg.includes("file already exists") ||
        errorMsg.includes("already exists")
      ) {
        console.log("[ImageCache] 缓存目录已存在，跳过创建");
      } else {
        console.error("[ImageCache] 缓存目录初始化失败:", error);
        // 目录创建失败，禁用缓存功能
        this.enabled = false;
        console.warn("[ImageCache] 由于目录创建失败，缓存功能已禁用");
        return;
      }
    }

    // 根据配置决定是否自动清理过期缓存
    if (IMAGE_CACHE.CLEANUP.AUTO_CLEANUP_ON_START) {
      this.cleanExpiredCache();
    }
  }

  /**
   * 生成缓存文件的键值
   * @param {string} url - 原始图片URL
   * @returns {string} 缓存键值
   */
  generateCacheKey(url) {
    // 使用简单的哈希函数生成唯一键值
    let hash = 0;
    for (let i = 0; i < url.length; i++) {
      const char = url.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 获取缓存元数据
   * @returns {Object} 缓存元数据
   */
  getCacheMetadata() {
    try {
      const metadata = wx.getStorageSync(this.metaDataKey);
      return metadata || {};
    } catch (error) {
      console.error("[ImageCache] 获取缓存元数据失败:", error);
      return {};
    }
  }

  /**
   * 保存缓存元数据
   * @param {Object} metadata - 缓存元数据
   */
  saveCacheMetadata(metadata) {
    try {
      wx.setStorageSync(this.metaDataKey, metadata);
    } catch (error) {
      console.error("[ImageCache] 保存缓存元数据失败:", error);
    }
  }

  /**
   * 检查缓存是否存在且有效
   * @param {string} url - 图片URL
   * @returns {Promise<string|null>} 缓存文件路径或null
   */
  async checkCache(url) {
    if (!this.enabled) {
      return null;
    }

    const cacheKey = this.generateCacheKey(url);
    const cachePath = `${this.cacheDir}/${cacheKey}`;
    const metadata = this.getCacheMetadata();

    // 检查元数据中是否存在该缓存
    if (!metadata[cacheKey]) {
      return null;
    }

    const cacheInfo = metadata[cacheKey];
    const now = Date.now();
    const expireTime =
      cacheInfo.timestamp + this.cacheExpireDays * 24 * 60 * 60 * 1000;

    // 检查是否过期
    if (now > expireTime) {
      console.log(`[ImageCache] 缓存已过期: ${cacheKey}`);
      await this.removeCache(cacheKey);
      return null;
    }

    // 检查文件是否实际存在
    try {
      this.fileSystemManager.accessSync(cachePath);
      console.log(`[ImageCache] 使用缓存: ${cacheKey}`);

      // 更新访问时间
      cacheInfo.lastAccess = now;
      metadata[cacheKey] = cacheInfo;
      this.saveCacheMetadata(metadata);

      return cachePath;
    } catch (error) {
      console.log(`[ImageCache] 缓存文件不存在: ${cacheKey}`);
      // 文件不存在，清理元数据
      delete metadata[cacheKey];
      this.saveCacheMetadata(metadata);
      return null;
    }
  }

  /**
   * 下载并缓存图片
   * @param {string} url - 图片URL
   * @returns {Promise<string>} 缓存文件路径
   */
  async downloadAndCache(url) {
    if (!this.enabled) {
      return url;
    }

    return new Promise((resolve, reject) => {
      // 添加到下载队列
      this.downloadQueue.push({ url, resolve, reject });
      this.processDownloadQueue();
    });
  }

  /**
   * 处理下载队列
   */
  async processDownloadQueue() {
    if (
      this.activeDownloads >= this.maxConcurrentDownloads ||
      this.downloadQueue.length === 0
    ) {
      return;
    }

    const { url, resolve, reject } = this.downloadQueue.shift();
    this.activeDownloads++;

    try {
      const result = await this.executeDownload(url);
      resolve(result);
    } catch (error) {
      reject(error);
    } finally {
      this.activeDownloads--;
      // 处理队列中的下一个任务
      this.processDownloadQueue();
    }
  }

  /**
   * 执行图片下载
   * @param {string} url - 图片URL
   * @returns {Promise<string>} 缓存文件路径
   */
  async executeDownload(url) {
    const cacheKey = this.generateCacheKey(url);
    const cachePath = `${this.cacheDir}/${cacheKey}`;

    console.log(`[ImageCache] 开始下载图片: ${url}`);

    return new Promise((resolve, reject) => {
      wx.downloadFile({
        url: url,
        success: (res) => {
          if (res.statusCode === 200) {
            try {
              // 将临时文件移动到缓存目录
              this.fileSystemManager.saveFileSync(res.tempFilePath, cachePath);

              // 更新元数据
              const metadata = this.getCacheMetadata();
              metadata[cacheKey] = {
                url: url,
                timestamp: Date.now(),
                lastAccess: Date.now(),
                size: this.getFileSize(cachePath),
              };
              this.saveCacheMetadata(metadata);

              console.log(`[ImageCache] 图片下载并缓存成功: ${cacheKey}`);
              resolve(cachePath);
            } catch (error) {
              console.error(
                `[ImageCache] 保存图片到缓存失败: ${cacheKey}`,
                error
              );
              reject(error);
            }
          } else {
            const error = new Error(`下载失败，状态码: ${res.statusCode}`);
            console.error(`[ImageCache] 图片下载失败: ${url}`, error);
            reject(error);
          }
        },
        fail: (error) => {
          console.error(`[ImageCache] 图片下载失败: ${url}`, error);
          reject(error);
        },
      });
    });
  }

  /**
   * 获取文件大小
   * @param {string} filePath - 文件路径
   * @returns {number} 文件大小（字节）
   */
  getFileSize(filePath) {
    try {
      const stats = this.fileSystemManager.statSync(filePath);
      return stats.size || 0;
    } catch (error) {
      console.error("[ImageCache] 获取文件大小失败:", error);
      return 0;
    }
  }

  /**
   * 获取缓存图片（带自动下载）
   * @param {string} url - 图片URL
   * @returns {Promise<string>} 图片路径（缓存路径或原URL）
   */
  async getImage(url) {
    if (!url || typeof url !== "string") {
      return this.placeholderImage;
    }

    // 如果不是网络URL，直接返回
    if (!url.startsWith("http") && !url.includes("tempFileURL")) {
      return url;
    }

    // 如果缓存未启用，直接返回原URL
    if (!this.enabled) {
      return url;
    }

    try {
      // 检查缓存
      const cachedPath = await this.checkCache(url);
      if (cachedPath) {
        return cachedPath;
      }

      // 缓存不存在，下载并缓存
      const downloadedPath = await this.downloadAndCache(url);
      return downloadedPath;
    } catch (error) {
      console.error("[ImageCache] 获取图片失败:", error);
      // 返回原URL作为备选方案
      return url;
    }
  }

  /**
   * 批量获取图片
   * @param {Array<string>} urls - 图片URL数组
   * @returns {Promise<Array<string>>} 图片路径数组
   */
  async getBatchImages(urls) {
    if (!this.enabled) {
      return urls;
    }

    const promises = urls.map((url) => this.getImage(url));
    return Promise.all(promises);
  }

  /**
   * 移除指定缓存
   * @param {string} cacheKey - 缓存键值
   */
  async removeCache(cacheKey) {
    try {
      const cachePath = `${this.cacheDir}/${cacheKey}`;
      this.fileSystemManager.unlinkSync(cachePath);

      // 更新元数据
      const metadata = this.getCacheMetadata();
      delete metadata[cacheKey];
      this.saveCacheMetadata(metadata);

      console.log(`[ImageCache] 移除缓存成功: ${cacheKey}`);
    } catch (error) {
      console.error(`[ImageCache] 移除缓存失败: ${cacheKey}`, error);
    }
  }

  /**
   * 清理过期缓存
   */
  async cleanExpiredCache() {
    if (!this.enabled) {
      return;
    }

    try {
      const metadata = this.getCacheMetadata();
      const now = Date.now();
      const expireTime = this.cacheExpireDays * 24 * 60 * 60 * 1000;

      let cleanedCount = 0;
      for (const [cacheKey, cacheInfo] of Object.entries(metadata)) {
        if (now - cacheInfo.timestamp > expireTime) {
          await this.removeCache(cacheKey);
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        console.log(`[ImageCache] 清理过期缓存 ${cleanedCount} 个`);
      }
    } catch (error) {
      console.error("[ImageCache] 清理过期缓存失败:", error);
    }
  }

  /**
   * 清理所有缓存
   */
  async clearAllCache() {
    if (!this.enabled) {
      console.log("[ImageCache] 缓存未启用，无需清理");
      return;
    }

    try {
      const metadata = this.getCacheMetadata();

      for (const cacheKey of Object.keys(metadata)) {
        await this.removeCache(cacheKey);
      }

      console.log("[ImageCache] 清理所有缓存完成");
    } catch (error) {
      console.error("[ImageCache] 清理所有缓存失败:", error);
    }
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  getCacheStats() {
    if (!this.enabled) {
      return { count: 0, totalSize: 0, totalSizeMB: "0", enabled: false };
    }

    try {
      const metadata = this.getCacheMetadata();
      const cacheKeys = Object.keys(metadata);

      let totalSize = 0;
      for (const cacheInfo of Object.values(metadata)) {
        totalSize += cacheInfo.size || 0;
      }

      return {
        count: cacheKeys.length,
        totalSize: totalSize,
        totalSizeMB: (totalSize / (1024 * 1024)).toFixed(2),
        enabled: this.enabled,
        maxSizeMB: (this.maxCacheSize / (1024 * 1024)).toFixed(0),
        expireDays: this.cacheExpireDays,
      };
    } catch (error) {
      console.error("[ImageCache] 获取缓存统计失败:", error);
      return {
        count: 0,
        totalSize: 0,
        totalSizeMB: "0",
        enabled: this.enabled,
      };
    }
  }

  /**
   * 检查并管理缓存大小
   */
  async manageCacheSize() {
    if (!this.enabled) {
      return;
    }

    try {
      const stats = this.getCacheStats();

      if (stats.totalSize > this.maxCacheSize) {
        console.log(
          `[ImageCache] 缓存超过限制 ${stats.totalSizeMB}MB，开始清理`
        );

        const metadata = this.getCacheMetadata();

        // 按最后访问时间排序，清理最旧的缓存
        const sortedCaches = Object.entries(metadata).sort(
          ([, a], [, b]) => a.lastAccess - b.lastAccess
        );

        let currentSize = stats.totalSize;
        const targetSize = this.maxCacheSize * this.targetSizeRatio;

        for (const [cacheKey, cacheInfo] of sortedCaches) {
          if (currentSize <= targetSize) break;

          await this.removeCache(cacheKey);
          currentSize -= cacheInfo.size || 0;
        }

        console.log("[ImageCache] 缓存清理完成");
      }
    } catch (error) {
      console.error("[ImageCache] 管理缓存大小失败:", error);
    }
  }

  /**
   * 设置缓存启用状态
   * @param {boolean} enabled - 是否启用
   */
  setEnabled(enabled) {
    this.enabled = enabled;
    console.log(`[ImageCache] 缓存${enabled ? "已启用" : "已禁用"}`);
  }

  /**
   * 获取缓存启用状态
   * @returns {boolean} 是否启用
   */
  isEnabled() {
    return this.enabled;
  }
}

// 创建单例实例
const imageCacheManager = new ImageCacheManager();

module.exports = imageCacheManager;

// 云函数入口文件
const cloud = require("wx-server-sdk");

// 云环境ID
const CLOUD_ENV_ID = "your-cloud-env-id"; // 替换为你的云环境ID

// 初始化云环境
cloud.init({
  env: CLOUD_ENV_ID,
});

/**
 * 从云存储获取JSON文件内容
 * @param {Object} event - 云函数调用参数
 * @param {string} event.filePath - 云存储中的文件路径，如 '数据20250427/tools_new_20250427.json'
 * @param {string} event.operation - 操作类型，可选值: 'getJson'(默认), 'getIcons'
 * @param {Array} event.iconPaths - 当 operation 为 'getIcons' 时，需要获取临时链接的图标路径数组
 * @param {string} event.envId - 可选，云环境ID，默认使用初始化的环境ID
 */
exports.main = async (event, context) => {
  const {
    filePath,
    operation = "getJson",
    iconPaths = [],
    envId = CLOUD_ENV_ID,
  } = event;

  console.log(
    `云函数调用，操作: ${operation}, 环境ID: ${envId}, 文件路径: ${filePath}`
  );

  // 获取图标临时链接
  if (operation === "getIcons") {
    try {
      if (!iconPaths || !Array.isArray(iconPaths) || iconPaths.length === 0) {
        return {
          success: false,
          error: "缺少图标路径参数或参数格式不正确",
          data: null,
        };
      }

      console.log(`开始获取图标临时链接，共 ${iconPaths.length} 个图标`);
      console.log("第一个路径示例:", iconPaths[0]);

      // 构建文件ID列表，不进行任何编码处理
      const validFileIDs = [];

      for (let i = 0; i < iconPaths.length; i++) {
        const path = iconPaths[i];
        console.log(`处理图标[${i}]: ${path}`);

        // 验证文件路径格式
        if (
          !path ||
          typeof path !== "string" ||
          path.indexOf("cloud://") !== 0
        ) {
          console.error(`图标路径[${i}]格式无效: ${path}`);
          continue;
        }

        // 直接使用原始路径，不进行编码
        validFileIDs.push({
          fileID: path,
          maxAge: 86400, // 临时链接有效期一天
        });
      }

      if (validFileIDs.length === 0) {
        return {
          success: false,
          error: "没有有效的图标文件ID",
          data: [],
        };
      }

      console.log(`验证后的有效图标文件数: ${validFileIDs.length}`);

      // ✨ 分批处理逻辑，解决微信云开发50个文件限制
      console.log("开始分批处理图标文件，每批最多50个...");
      const batchSize = 50;
      const allResults = [];
      const totalBatches = Math.ceil(validFileIDs.length / batchSize);

      console.log(
        `总共需要处理 ${totalBatches} 批，每批最多 ${batchSize} 个文件`
      );

      for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
        const startIndex = batchIndex * batchSize;
        const endIndex = Math.min(startIndex + batchSize, validFileIDs.length);
        const currentBatch = validFileIDs.slice(startIndex, endIndex);

        console.log(
          `处理第 ${batchIndex + 1}/${totalBatches} 批：文件 ${
            startIndex + 1
          }-${endIndex}`
        );

        try {
          // 直接使用原始文件ID，不进行任何编码处理
          console.log(`批次${batchIndex + 1}包含${currentBatch.length}个文件:`);
          currentBatch.forEach((fileItem, index) => {
            console.log(`  [${index}] ${fileItem.fileID}`);
          });

          // 调用微信云开发API获取当前批次的临时链接
          const batchResult = await cloud.getTempFileURL({
            fileList: currentBatch,
          });

          if (!batchResult || !batchResult.fileList) {
            console.error(
              `第${batchIndex + 1}批次getTempFileURL返回结果无效:`,
              batchResult
            );
            // 为当前批次的文件添加错误状态
            const errorResults = currentBatch.map((item) => ({
              fileID: item.fileID,
              status: -1,
              errMsg: "批次处理失败：返回结果无效",
            }));
            allResults.push(...errorResults);
            continue;
          }

          // 检查当前批次结果中的错误
          const batchFileList = batchResult.fileList || [];
          console.log(
            `第${batchIndex + 1}批次获取临时链接结果: 共${
              batchFileList.length
            }个文件`
          );

          for (let i = 0; i < batchFileList.length; i++) {
            const file = batchFileList[i];
            if (file.status !== 0) {
              console.error(
                `批次${batchIndex + 1}，文件[${i}] ${
                  file.fileID
                } 获取临时链接失败: 状态=${file.status}, 错误=${file.errMsg}`
              );
            } else {
              console.log(
                `批次${batchIndex + 1}，文件[${i}] 获取临时链接成功: ${
                  file.tempFileURL
                }`
              );
            }
          }

          // 将当前批次结果添加到总结果中
          allResults.push(...batchFileList);

          // 添加批次间的延迟，避免并发过高
          if (batchIndex < totalBatches - 1) {
            console.log("批次间延迟100ms...");
            await new Promise((resolve) => setTimeout(resolve, 100));
          }
        } catch (batchError) {
          console.error(`第${batchIndex + 1}批次处理失败:`, batchError);
          // 为当前批次的文件添加错误状态
          const errorResults = currentBatch.map((item) => ({
            fileID: item.fileID,
            status: -1,
            errMsg: `批次处理异常: ${batchError.message}`,
          }));
          allResults.push(...errorResults);
        }
      }

      console.log(`所有批次处理完成，总计处理了 ${allResults.length} 个文件`);

      // 统计成功和失败的数量
      const successCount = allResults.filter(
        (file) => file.status === 0
      ).length;
      const failCount = allResults.length - successCount;
      console.log(
        `处理结果统计：成功 ${successCount} 个，失败 ${failCount} 个`
      );

      return {
        success: true,
        data: allResults,
        error: null,
        debug: {
          totalFiles: validFileIDs.length,
          totalBatches: totalBatches,
          successCount: successCount,
          failCount: failCount,
        },
      };
    } catch (error) {
      console.error("获取图标临时链接失败:", error);
      return {
        success: false,
        error: error.message || "获取图标临时链接失败",
        data: null,
      };
    }
  }

  // 以下是获取JSON数据的默认操作
  if (!filePath) {
    console.error("错误：缺少文件路径参数");
    return {
      success: false,
      error: "缺少文件路径参数",
      data: null,
    };
  }

  try {
    console.log(`开始从云存储读取文件: ${filePath}`);

    // 检查filePath是否包含数据版本文件夹
    const pathParts = filePath.split("/");
    const hasDataVersionFolder = pathParts.some(
      (part) =>
        part.startsWith("数据") &&
        part.length === 10 &&
        !isNaN(part.substring(2))
    );

    if (hasDataVersionFolder) {
      console.log(`✓ 文件路径包含数据版本文件夹: ${filePath}`);
    } else {
      console.log(`⚠️ 文件路径不包含数据版本文件夹: ${filePath}`);
    }

    // 构建完整的云存储文件ID
    const fileID = `cloud://${envId}.636c-${envId}-1349397796/${filePath}`;
    console.log(`构建的完整云存储文件ID: ${fileID}`);

    // 1. 先检查文件是否存在
    console.log("步骤1: 检查文件是否存在...");
    try {
      const statResult = await cloud.file().stat({
        fileID: fileID,
      });
      console.log("✓ 文件存在性检查结果:", statResult);
    } catch (statError) {
      console.error("⚠️ 文件存在性检查失败:", statError);
      console.error("这可能不影响后续操作，继续尝试获取临时链接...");
    }

    // 2. 获取文件临时链接
    console.log("步骤2: 获取文件临时链接...");
    const fileResult = await cloud.getTempFileURL({
      fileList: [
        {
          fileID: fileID,
          maxAge: 86400, // 临时链接有效期一天
        },
      ],
    });

    console.log(
      "getTempFileURL 完整返回结果:",
      JSON.stringify(fileResult, null, 2)
    );

    if (!fileResult.fileList || fileResult.fileList.length === 0) {
      console.error("❌ getTempFileURL返回的fileList为空或不存在");
      return {
        success: false,
        error: "获取文件临时链接失败: fileList为空",
        data: null,
        debug: {
          fileID: fileID,
          fileResult: fileResult,
        },
      };
    }

    const firstFile = fileResult.fileList[0];
    if (firstFile.status !== 0) {
      console.error("❌ 获取文件临时链接失败:", {
        status: firstFile.status,
        errMsg: firstFile.errMsg,
        fileID: firstFile.fileID,
      });
      return {
        success: false,
        error: `获取文件临时链接失败: ${firstFile.errMsg || "未知错误"}`,
        data: null,
        debug: {
          fileID: fileID,
          status: firstFile.status,
          errMsg: firstFile.errMsg,
        },
      };
    }

    const tempFileURL = firstFile.tempFileURL;
    console.log(`✓ 成功获取到文件临时链接: ${tempFileURL}`);

    // 3. 云函数中下载文件内容（不受域名限制）
    console.log("步骤3: 下载文件内容...");
    const axios = require("axios");

    console.log("开始使用axios下载文件...");
    console.log("原始临时URL:", tempFileURL);

    // 对URL进行编码处理，解决中文字符问题（仅针对axios请求）
    const encodedURL = encodeURI(tempFileURL);
    console.log("编码后的URL:", encodedURL);

    const response = await axios.get(encodedURL, {
      timeout: 30000, // 30秒超时
      headers: {
        "User-Agent": "WeChat-MiniProgram CloudFunction",
      },
    });

    console.log(`✓ axios响应状态: ${response.status}`);
    console.log(`✓ 响应数据类型: ${typeof response.data}`);

    if (!response || !response.data) {
      console.error("❌ axios响应无效或数据为空");
      return {
        success: false,
        error: "下载文件内容失败: 响应数据为空",
        data: null,
        debug: {
          responseStatus: response?.status,
          responseDataType: typeof response?.data,
        },
      };
    }

    // 检查是否为有效的JSON数据
    if (typeof response.data === "object") {
      console.log(
        `✓ 成功获取JSON数据，categories数量: ${
          response.data.categories?.length || 0
        }, tools数量: ${response.data.tools?.length || 0}`
      );
    } else if (typeof response.data === "string") {
      console.log("⚠️ 获取的数据是字符串格式，尝试解析为JSON...");
      try {
        const jsonData = JSON.parse(response.data);
        console.log(
          `✓ 成功解析JSON数据，categories数量: ${
            jsonData.categories?.length || 0
          }, tools数量: ${jsonData.tools?.length || 0}`
        );
        response.data = jsonData;
      } catch (parseError) {
        console.error("❌ JSON解析失败:", parseError.message);
        return {
          success: false,
          error: `JSON解析失败: ${parseError.message}`,
          data: null,
          debug: {
            dataType: typeof response.data,
            dataPreview: String(response.data).substring(0, 200),
          },
        };
      }
    }

    // 4. 返回文件内容
    console.log("✓ 云函数执行成功，返回数据");
    return {
      success: true,
      data: response.data,
      error: null,
    };
  } catch (error) {
    console.error("❌ 云函数执行出现异常:", error);
    console.error("错误详情:", {
      name: error.name,
      message: error.message,
      stack: error.stack,
      code: error.code,
    });

    return {
      success: false,
      error: error.message || "云函数执行错误",
      data: null,
      debug: {
        errorName: error.name,
        errorCode: error.code,
        filePath: filePath,
        envId: envId,
      },
    };
  }
};

Page({
  data: {
    // version: "1.0.0", // 暂时硬编码版本号
    year: new Date().getFullYear(),
    wechatID: "pialio", // 请替换为你的微信号
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 可以在这里尝试动态获取版本号，例如从 app.js 或其他配置中读取
    // const accountInfo = wx.getAccountInfoSync();
    // this.setData({
    //   version: accountInfo.miniProgram.version
    // });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},

  copyWechatID() {
    wx.setClipboardData({
      data: this.data.wechatID,
      success: () => {
        wx.showToast({
          title: "微信号已复制",
          icon: "success",
          duration: 1500,
        });
      },
      fail: () => {
        wx.showToast({
          title: "复制失败",
          icon: "none",
          duration: 1500,
        });
      },
    });
  },
});

const app = getApp();
let that = null;
Page({
  onLoad(options) {
    that = this;
    if (options.url != null) {
      this.setData({
        webUrl: options.url,
      });
      if (options.title != null) {
        wx.setNavigationBarTitle({
          title: options.title,
        });
      }
    } else {
      wx.navigateBack({
        delta: 1,
      });
    }
  },
});

