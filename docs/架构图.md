```mermaid
graph TD
    %% 用户层
    User[用户] -->|访问| MiniProgram[微信小程序前端]
    
    %% 前端层
    subgraph "前端层 (微信小程序)"
        MiniProgram --> Pages[页面组件]
        Pages -->|包含| IndexPage[首页]
        Pages -->|包含| DetailPage[工具详情页]
        Pages -->|包含| AboutPage[关于页面]
        
        MiniProgram --> Utils[工具类]
        Utils -->|包含| CloudManager[云函数管理器]
        
        MiniProgram --> Components[自定义组件]
        Components -->|包含| Skeleton[骨架屏组件]
    end
    
    %% 云函数层
    subgraph "云函数层"
        CloudFunction[云函数 getJsonData]
        CloudFunction -->|操作1| GetJson[获取JSON数据]
        CloudFunction -->|操作2| GetIcons[获取图标临时链接]
    end
    
    %% 数据存储层
    subgraph "数据存储层 (云存储)"
        CloudStorage[云存储]
        CloudStorage --> JsonData[tools_new.json]
        CloudStorage --> IconStorage[图标资源]
        
        JsonData -->|包含| Categories[分类数据]
        JsonData -->|包含| Tools[工具数据]
        
        IconStorage -->|按分类存储| CategoryIcons[分类图标目录]
    end
    
    %% 数据流向
    CloudManager -->|调用| CloudFunction
    CloudFunction -->|读取| CloudStorage
    
    %% 内存缓存
    subgraph "内存缓存"
        MemoryCache[内存缓存]
        MemoryCache -->|存储| CachedCategories[分类数据]
        MemoryCache -->|存储| CachedTools[工具数据]
    end
    
    CloudManager -->|更新| MemoryCache
    IndexPage -->|读取| MemoryCache
    DetailPage -->|读取| MemoryCache
    
    %% 数据更新流程
    UpdateFlow[数据更新流程]
    UpdateFlow -->|1.修改| LocalJson[本地JSON文件]
    UpdateFlow -->|2.准备| LocalIcons[本地图标]
    UpdateFlow -->|3.上传| CloudStorage
    
    %% 样式设置
    classDef frontend fill:#d4f1f9,stroke:#05a,stroke-width:2px;
    classDef cloud fill:#ffe6cc,stroke:#d79b00,stroke-width:2px;
    classDef storage fill:#e1d5e7,stroke:#9673a6,stroke-width:2px;
    classDef cache fill:#d5e8d4,stroke:#82b366,stroke-width:2px;
    classDef update fill:#fff2cc,stroke:#d6b656,stroke-width:2px;
    
    class MiniProgram,Pages,IndexPage,DetailPage,AboutPage,Utils,CloudManager,Components,Skeleton frontend;
    class CloudFunction,GetJson,GetIcons cloud;
    class CloudStorage,JsonData,IconStorage,Categories,Tools,CategoryIcons storage;
    class MemoryCache,CachedCategories,CachedTools cache;
    class UpdateFlow,LocalJson,LocalIcons update;
```
