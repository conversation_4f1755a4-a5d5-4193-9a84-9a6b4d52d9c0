# 图片缓存功能使用指南

## 功能概述

图片缓存功能是为云端模式专门设计的性能优化功能，通过将云存储的图片下载到本地，减少重复的网络请求，显著提升用户体验。

## 主要特性

### 🚀 自动缓存

- 首次访问图片时自动下载并缓存到本地
- 后续访问直接使用本地缓存，加载速度更快
- 支持批量处理，提升整体性能

### 🗂️ 智能管理

- 自动过期清理（默认 7 天）
- 智能大小管理（默认 50MB 限制）
- LRU 清理策略（最近最少使用优先清理）

### ⚙️ 灵活配置

- 可配置缓存大小限制
- 可配置缓存过期时间
- 可配置并发下载数
- 支持动态启用/禁用

## 配置说明

### 基础配置

```javascript
// miniprogram/config/config.js
const IMAGE_CACHE = {
  MAX_CACHE_SIZE: 50 * 1024 * 1024, // 50MB 最大缓存大小
  CACHE_EXPIRE_DAYS: 7, // 7天过期时间
  MAX_CONCURRENT_DOWNLOADS: 3, // 最大并发下载数
  ENABLED: true, // 是否启用缓存

  CLEANUP: {
    AUTO_CLEANUP_ON_START: true, // 启动时自动清理过期缓存
    TARGET_SIZE_RATIO: 0.8, // 清理到80%目标大小
  },

  PLACEHOLDER_IMAGE: "/images/placeholder/tool-icon.png",
};
```

### 数据源配置

```javascript
// 切换到云端模式以启用图片缓存
const DATA_SOURCE = {
  TYPE: "cloud", // 必须为 "cloud" 才能启用图片缓存
  // ... 其他配置
};
```

## 使用方法

### 1. 启用缓存

确保在配置文件中启用了图片缓存：

```javascript
// 方法1：配置文件中启用
IMAGE_CACHE.ENABLED = true;

// 方法2：运行时动态启用
imageCacheManager.setEnabled(true);
```

### 2. 切换到云端模式

```javascript
// miniprogram/config/config.js
const DATA_SOURCE = {
  TYPE: "cloud", // 切换到云端模式
  // ...
};
```

### 3. 使用缓存管理页面

访问缓存管理页面查看和管理缓存：

```javascript
// 导航到缓存管理页面
wx.navigateTo({
  url: "/pages/cache-manager/index",
});
```

## API 接口

### 图片缓存管理器 API

#### 获取单张图片

```javascript
const imageCacheManager = require("./utils/imageCacheManager");

// 获取图片（自动缓存）
const cachedPath = await imageCacheManager.getImage(imageUrl);
```

#### 批量获取图片

```javascript
// 批量获取图片（并发处理）
const imageUrls = ["url1", "url2", "url3"];
const cachedPaths = await imageCacheManager.getBatchImages(imageUrls);
```

#### 缓存管理

```javascript
// 获取缓存统计
const stats = imageCacheManager.getCacheStats();

// 清理所有缓存
await imageCacheManager.clearAllCache();

// 清理过期缓存
await imageCacheManager.cleanExpiredCache();

// 管理缓存大小
await imageCacheManager.manageCacheSize();
```

#### 缓存状态控制

```javascript
// 启用/禁用缓存
imageCacheManager.setEnabled(true); // 启用
imageCacheManager.setEnabled(false); // 禁用

// 检查缓存状态
const isEnabled = imageCacheManager.isEnabled();
```

### 数据管理器集成 API

```javascript
const dataManagerFactory = require("./utils/dataManagerFactory");
const dataManager = dataManagerFactory.getDataManager();

// 获取缓存统计（仅云端模式）
const stats = dataManager.getImageCacheStats();

// 清理图片缓存（仅云端模式）
await dataManager.clearImageCache();
```

## 工作流程

### 图片加载流程

1. **检查缓存**: 首先检查本地是否有有效缓存
2. **返回缓存**: 如果缓存存在且未过期，直接返回本地路径
3. **下载缓存**: 如果缓存不存在，下载图片并保存到本地
4. **返回路径**: 返回本地缓存路径给页面使用

### 缓存清理流程

1. **过期检查**: 启动时自动检查过期缓存
2. **大小管理**: 定期检查缓存大小是否超限
3. **LRU 清理**: 超限时按最近访问时间清理旧缓存
4. **元数据更新**: 同步更新缓存元数据

## 性能优化建议

### 1. 合理设置缓存大小

```javascript
// 根据用户设备存储空间调整
IMAGE_CACHE.MAX_CACHE_SIZE = 30 * 1024 * 1024; // 30MB for 低端设备
IMAGE_CACHE.MAX_CACHE_SIZE = 100 * 1024 * 1024; // 100MB for 高端设备
```

### 2. 优化并发下载

```javascript
// 根据网络条件调整并发数
IMAGE_CACHE.MAX_CONCURRENT_DOWNLOADS = 2; // 弱网环境
IMAGE_CACHE.MAX_CONCURRENT_DOWNLOADS = 5; // 强网环境
```

### 3. 合理设置过期时间

```javascript
// 根据图片更新频率调整
IMAGE_CACHE.CACHE_EXPIRE_DAYS = 3; // 高频更新
IMAGE_CACHE.CACHE_EXPIRE_DAYS = 14; // 低频更新
```

## 监控和调试

### 缓存统计信息

```javascript
const stats = imageCacheManager.getCacheStats();
console.log(`缓存文件数: ${stats.count}`);
console.log(`缓存大小: ${stats.totalSizeMB} MB`);
console.log(`缓存状态: ${stats.enabled ? "启用" : "禁用"}`);
```

### 控制台日志

缓存管理器会输出详细的调试日志：

```
[ImageCache] 缓存目录初始化成功
[ImageCache] 使用缓存: abc123
[ImageCache] 开始下载图片: https://...
[ImageCache] 图片下载并缓存成功: abc123
[ImageCache] 清理过期缓存 5 个
```

## 故障排除

### 常见问题

#### 1. 缓存不生效

- 检查是否为云端模式 (`DATA_SOURCE.TYPE = "cloud"`)
- 检查缓存是否启用 (`IMAGE_CACHE.ENABLED = true`)
- 查看控制台日志确认缓存流程

#### 2. 图片加载慢

- 检查网络连接状态
- 调整并发下载数配置
- 查看缓存命中率

#### 3. 存储空间不足

- 减小最大缓存大小配置
- 缩短缓存过期时间
- 手动清理缓存

#### 4. 缓存目录创建失败

- 检查小程序存储权限
- 查看控制台错误日志
- 重启小程序重新初始化

### 调试技巧

#### 1. 启用详细日志

```javascript
// 在开发环境中启用更多日志
if (__DEV__) {
  console.log("[Debug] 图片缓存调试模式");
}
```

#### 2. 强制清理缓存

```javascript
// 测试时强制清理所有缓存
await imageCacheManager.clearAllCache();
```

#### 3. 禁用缓存测试

```javascript
// 对比缓存启用/禁用的性能差异
imageCacheManager.setEnabled(false);
```

## 注意事项

1. **仅云端模式**: 图片缓存仅在云端模式 (`TYPE: "cloud"`) 下有效
2. **存储限制**: 缓存会占用小程序本地存储，注意存储空间管理
3. **网络依赖**: 首次访问仍需网络下载，后续访问才能享受缓存优势
4. **数据同步**: 云端图片更新后，本地缓存可能需要时间同步
5. **隐私安全**: 缓存文件存储在应用沙盒内，卸载应用时会自动清理

## 最佳实践

1. **生产环境启用**: 生产环境建议启用图片缓存以提升用户体验
2. **开发环境灵活**: 开发环境可根据需要启用/禁用缓存
3. **定期监控**: 定期检查缓存使用情况，及时调整配置
4. **用户反馈**: 收集用户对图片加载速度的反馈，优化缓存策略
5. **版本升级**: 大版本更新时可考虑清理旧缓存
