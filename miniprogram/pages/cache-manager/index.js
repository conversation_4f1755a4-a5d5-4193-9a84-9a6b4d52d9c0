const dataManagerFactory = require("../../utils/dataManagerFactory");
const imageCacheManager = require("../../utils/imageCacheManager");

Page({
  data: {
    cacheStats: {
      count: 0,
      totalSizeMB: "0",
      maxSizeMB: "50",
      enabled: false,
      expireDays: 7,
    },
    progressPercent: 0,
    dataSourceType: "",
  },

  onLoad() {
    // 获取数据源类型
    const dataSourceType = dataManagerFactory.getDataSourceType();
    this.setData({ dataSourceType });

    // 加载缓存统计
    this.loadCacheStats();
  },

  onShow() {
    // 页面显示时刷新统计
    this.loadCacheStats();
  },

  /**
   * 加载缓存统计信息
   */
  loadCacheStats() {
    try {
      const stats = imageCacheManager.getCacheStats();

      // 计算进度百分比
      const maxSize = parseFloat(stats.maxSizeMB) || 50;
      const currentSize = parseFloat(stats.totalSizeMB) || 0;
      const progressPercent =
        maxSize > 0 ? Math.min((currentSize / maxSize) * 100, 100) : 0;

      this.setData({
        cacheStats: stats,
        progressPercent: Math.round(progressPercent),
      });

      console.log("[CacheManager] 缓存统计加载完成:", stats);
    } catch (error) {
      console.error("[CacheManager] 加载缓存统计失败:", error);
      wx.showToast({
        title: "加载统计失败",
        icon: "none",
      });
    }
  },

  /**
   * 刷新统计信息
   */
  refreshStats() {
    wx.showLoading({ title: "刷新中..." });

    // 延迟一下给用户反馈
    setTimeout(() => {
      this.loadCacheStats();
      wx.hideLoading();
      wx.showToast({
        title: "刷新完成",
        icon: "success",
        duration: 1500,
      });
    }, 500);
  },

  /**
   * 清空缓存
   */
  async clearCache() {
    // 确认对话框
    const result = await this.showConfirmDialog(
      "确定要清空所有图片缓存吗？",
      "清空后需要重新下载图片"
    );

    if (!result) {
      return;
    }

    wx.showLoading({ title: "清理中..." });

    try {
      // 获取云端数据管理器（如果是云端模式）
      const dataManager = dataManagerFactory.getDataManager();

      if (typeof dataManager.clearImageCache === "function") {
        await dataManager.clearImageCache();
      } else {
        await imageCacheManager.clearAllCache();
      }

      // 刷新统计
      this.loadCacheStats();

      wx.hideLoading();
      wx.showToast({
        title: "清理完成",
        icon: "success",
      });

      console.log("[CacheManager] 缓存清理完成");
    } catch (error) {
      console.error("[CacheManager] 清理缓存失败:", error);
      wx.hideLoading();
      wx.showToast({
        title: "清理失败",
        icon: "none",
      });
    }
  },

  /**
   * 切换缓存启用状态
   */
  toggleCache() {
    if (this.data.dataSourceType !== "cloud") {
      wx.showToast({
        title: "仅云端模式支持图片缓存",
        icon: "none",
        duration: 2000,
      });
      return;
    }

    const currentEnabled = this.data.cacheStats.enabled;
    const newEnabled = !currentEnabled;

    imageCacheManager.setEnabled(newEnabled);

    // 刷新统计
    this.loadCacheStats();

    wx.showToast({
      title: newEnabled ? "缓存已启用" : "缓存已禁用",
      icon: "success",
    });
  },

  /**
   * 显示确认对话框
   * @param {string} title 标题
   * @param {string} content 内容
   * @returns {Promise<boolean>} 用户是否确认
   */
  showConfirmDialog(title, content) {
    return new Promise((resolve) => {
      wx.showModal({
        title: title,
        content: content,
        confirmText: "确定",
        cancelText: "取消",
        success: (res) => {
          resolve(res.confirm);
        },
        fail: () => {
          resolve(false);
        },
      });
    });
  },

  /**
   * 页面返回
   */
  onBack() {
    wx.navigateBack();
  },
});
