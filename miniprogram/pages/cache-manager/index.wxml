<!--pages/cache-manager/index.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-title">图片缓存管理</view>
  
  <!-- 缓存状态卡片 -->
  <view class="cache-status-card">
    <view class="card-title">缓存状态</view>
    
    <view class="status-item">
      <view class="status-label">启用状态</view>
      <view class="status-value {{cacheStats.enabled ? 'enabled' : 'disabled'}}">
        {{cacheStats.enabled ? '已启用' : '已禁用'}}
      </view>
    </view>
    
    <view class="status-item" wx:if="{{cacheStats.enabled}}">
      <view class="status-label">缓存数量</view>
      <view class="status-value">{{cacheStats.count}} 个文件</view>
    </view>
    
    <view class="status-item" wx:if="{{cacheStats.enabled}}">
      <view class="status-label">占用空间</view>
      <view class="status-value">{{cacheStats.totalSizeMB}} MB / {{cacheStats.maxSizeMB}} MB</view>
    </view>
    
    <view class="status-item" wx:if="{{cacheStats.enabled}}">
      <view class="status-label">过期时间</view>
      <view class="status-value">{{cacheStats.expireDays}} 天</view>
    </view>
  </view>
  
  <!-- 缓存进度条 -->
  <view class="progress-card" wx:if="{{cacheStats.enabled}}">
    <view class="card-title">存储使用率</view>
    <view class="progress-container">
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{progressPercent}}%"></view>
      </view>
      <view class="progress-text">{{progressPercent}}%</view>
    </view>
  </view>
  
  <!-- 操作按钮 -->
  <view class="action-buttons">
    <view class="action-button refresh" bindtap="refreshStats">
      <view class="button-icon">🔄</view>
      <view class="button-text">刷新统计</view>
    </view>
    
    <view class="action-button clear" bindtap="clearCache" wx:if="{{cacheStats.enabled && cacheStats.count > 0}}">
      <view class="button-icon">🗑️</view>
      <view class="button-text">清空缓存</view>
    </view>
    
    <view class="action-button toggle" bindtap="toggleCache">
      <view class="button-icon">{{cacheStats.enabled ? '❌' : '✅'}}</view>
      <view class="button-text">{{cacheStats.enabled ? '禁用缓存' : '启用缓存'}}</view>
    </view>
  </view>
  
  <!-- 说明信息 -->
  <view class="info-card">
    <view class="card-title">缓存说明</view>
    <view class="info-text">
      • 图片缓存可以减少网络请求，提升加载速度
    </view>
    <view class="info-text">
      • 缓存文件会自动过期清理
    </view>
    <view class="info-text">
      • 当缓存超过限制时会自动清理最旧的文件
    </view>
    <view class="info-text">
      • 仅在云端模式下有效
    </view>
  </view>
</view> 