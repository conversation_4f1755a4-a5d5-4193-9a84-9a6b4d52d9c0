/* pages/cache-manager/index.wxss */
.container {
    padding: 20rpx;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.page-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    text-align: center;
    margin-bottom: 30rpx;
}

/* 卡片通用样式 */
.cache-status-card,
.progress-card,
.info-card {
    background: white;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 24rpx;
    border-left: 6rpx solid #007aff;
    padding-left: 16rpx;
}

/* 状态项 */
.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
}

.status-item:last-child {
    border-bottom: none;
}

.status-label {
    font-size: 28rpx;
    color: #666;
}

.status-value {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
}

.status-value.enabled {
    color: #34c759;
}

.status-value.disabled {
    color: #ff3b30;
}

/* 进度条 */
.progress-container {
    display: flex;
    align-items: center;
    gap: 20rpx;
}

.progress-bar {
    flex: 1;
    height: 12rpx;
    background-color: #e5e5e5;
    border-radius: 6rpx;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007aff 0%, #5ac8fa 100%);
    border-radius: 6rpx;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 24rpx;
    color: #666;
    font-weight: 500;
    min-width: 60rpx;
    text-align: right;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    margin-bottom: 20rpx;
}

.action-button {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 16rpx;
    padding: 24rpx 30rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.action-button:active {
    transform: scale(0.98);
    opacity: 0.8;
}

.action-button.refresh {
    border-left: 6rpx solid #007aff;
}

.action-button.clear {
    border-left: 6rpx solid #ff3b30;
}

.action-button.toggle {
    border-left: 6rpx solid #34c759;
}

.button-icon {
    font-size: 32rpx;
    margin-right: 20rpx;
}

.button-text {
    font-size: 30rpx;
    color: #333;
    font-weight: 500;
}

/* 说明信息 */
.info-text {
    font-size: 26rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 12rpx;
}

.info-text:last-child {
    margin-bottom: 0;
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
    .container {
        padding: 15rpx;
    }

    .page-title {
        font-size: 32rpx;
    }

    .card-title {
        font-size: 28rpx;
    }

    .status-label,
    .status-value,
    .button-text {
        font-size: 26rpx;
    }
}