// pages/index/index.js
// 获取全局应用实例，可以用来获取全局数据或调用全局方法
const app = getApp();
// 引入数据管理器工厂和配置
const dataManagerFactory = require("../../utils/dataManagerFactory");
const toolsDataManager = dataManagerFactory.getDataManager();
const config = require("../../config/config");

// 定义页面
Page({
  // 页面的初始数据
  data: {
    categories: [], // 用于存储所有工具分类的数组
    currentTab: 0, // 当前激活的顶部标签页的索引，默认为第一个
    currentCategory: "", // 当前选中的分类的唯一标识符 (ID)
    currentProducts: [], // 当前选中分类下需要展示的工具产品列表
    tabScrollLeft: 0, // 恢复：标签栏的水平滚动位置
    dataSourceType: "", // 当前使用的数据源类型
    categoryProducts: {}, // 存储所有分类的产品数据，用于缓存已加载的数据
    showSwipeHint: false, // 是否显示滑动提示
  },

  // 页面加载时触发的生命周期函数
  onLoad() {
    // 设置数据源类型
    const dataSourceType = dataManagerFactory.getDataSourceType();
    this.setData({ dataSourceType });
    console.log(`当前页面数据源类型: ${dataSourceType}`);

    // 调用 loadData 方法加载页面所需数据
    this.loadData();

    // 每次打开都显示滑动提示
    // 延迟1秒显示滑动提示，等待页面加载完成
    setTimeout(() => {
      this.setData({ showSwipeHint: true });
      // 3秒后自动关闭提示
      setTimeout(() => {
        this.setData({ showSwipeHint: false });
      }, 2000);
    }, 1000);

    // 如果使用云端数据源，确保云环境已初始化
    if (dataSourceType === "cloud") {
      if (!wx.cloud) {
        console.error("云开发环境未初始化");
        wx.showToast({
          title: "云环境初始化失败",
          icon: "none",
        });
        return;
      }

      // 初始化云环境
      wx.cloud.init({
        env: dataManagerFactory.getCloudEnvId(),
        traceUser: true,
      });
    }
  },

  // 页面显示/切入前台时触发的生命周期函数
  onShow() {
    // 此处可以添加页面每次显示时需要执行的逻辑，例如刷新数据
    // 当前为空，没有特定逻辑
  },

  // 定义加载页面初始数据的方法
  loadData() {
    // 显示加载提示框，提升用户体验
    wx.showLoading({
      title: "加载中...", // 提示框文字内容
    });

    // 异步获取所有工具分类
    toolsDataManager
      .getCategories()
      .then((categories) => {
        // 成功获取分类数据后的回调
        // 检查获取到的分类数据是否有效
        if (!categories || !categories.length) {
          // 如果分类数据为空或无效，隐藏加载提示框
          wx.hideLoading();
          // 返回一个被拒绝的 Promise，中断后续链式调用，并传递错误信息
          return Promise.reject(new Error("分类数据为空"));
        }

        // 获取第一个分类的 ID，如果分类数组为空则设为空字符串
        const firstCategory = categories[0]?.id || "";
        // 更新页面的 data，设置分类列表和当前选中的分类（默认为第一个）
        this.setData({ categories, currentCategory: firstCategory });
        // 在控制台打印加载到的分类数据，方便调试
        console.log("加载的分类数据:", categories);

        // 基于第一个分类的 ID，异步获取该分类下的工具列表
        return toolsDataManager.getToolsByCategory(firstCategory);
      })
      .then((products) => {
        // 成功获取第一个分类的工具数据后的回调
        // 隐藏加载提示框
        wx.hideLoading();

        // 添加更详细的日志，特别是检查图片URL
        if (products.length > 0) {
          console.log(`首个产品加载成功，图标URL: ${products[0].icon}`);
          // 记录所有图片路径，用于调试
          products.forEach((product, index) => {
            console.log(
              `索引 ${index}, 产品名称: ${product.name}, 图片路径: ${
                product.icon
              }, 是否缓存: ${product.isCached || false}`
            );
          });
        }

        // 在控制台打印加载到的产品数据，方便调试
        console.log("加载的产品数据:", products);
        // 打印第一个产品的示例信息，或提示无产品数据
        console.log(
          "第一个产品示例:",
          products.length > 0 ? products[0] : "无产品数据"
        );

        // 图片已在数据管理器中处理完成，直接使用
        this.setData({
          currentProducts: products, // 设置当前分类的产品列表
        });
      })
      .catch((error) => {
        // 捕获在获取分类或工具数据过程中发生的任何错误
        // 在控制台打印错误信息，方便调试
        console.error("加载数据失败:", error);
        // 隐藏加载提示框
        wx.hideLoading();
        // 显示一个轻提示，告知用户加载失败
        wx.showToast({
          title: "加载失败，请重试", // 提示文字
          icon: "none", // 不显示图标
        });
      });
  },

  // 定义切换顶部标签页的方法
  switchTab(e) {
    const id = e.currentTarget.dataset.id; // 获取被点击标签对应的分类 ID
    const index = e.currentTarget.dataset.index; // 获取被点击标签的索引

    if (this.data.currentTab === index) {
      return;
    }

    this.setData({
      currentTab: index,
      currentCategory: id,
    });

    this.ensureTabVisible(index);

    this.loadCategoryData(id, index);
    this.preloadAdjacentCategories(index);
  },

  // 处理swiper切换事件
  handleSwiperChange(e) {
    const index = e.detail.current;
    console.log(`[handleSwiperChange] Swiper changed to index: ${index}`);
    if (!this.data.categories || index >= this.data.categories.length) {
      console.error(
        `[handleSwiperChange] Invalid index ${index} or categories data missing.`
      );
      return;
    }
    const id = this.data.categories[index].id;
    console.log(`[handleSwiperChange] Corresponding category ID: ${id}`);

    // 不再需要在 nextTick 中设置 scrollIntoView
    this.setData({
      currentTab: index,
      currentCategory: id,
    });

    this.ensureTabVisible(index);

    this.loadCategoryData(id, index);
    this.preloadAdjacentCategories(index);
  },

  // 加载指定分类的数据
  loadCategoryData(categoryId, index) {
    // 检查是否已缓存该分类的数据
    if (this.data.categoryProducts[categoryId]) {
      // 如果已缓存，直接使用缓存数据
      this.setData({
        currentProducts: this.data.categoryProducts[categoryId],
      });

      // 预加载相邻分类数据
      this.preloadAdjacentCategories(index);
      return;
    }

    // 否则，加载该分类的数据
    wx.showLoading({ title: "加载中..." });

    toolsDataManager
      .getToolsByCategory(categoryId)
      .then((products) => {
        wx.hideLoading();

        // 数据管理器已处理图片缓存，直接使用
        // 缓存该分类的数据
        const categoryProducts = { ...this.data.categoryProducts };
        categoryProducts[categoryId] = products;

        // 更新页面数据
        this.setData({
          currentProducts: products,
          categoryProducts,
        });

        // 预加载相邻分类数据
        this.preloadAdjacentCategories(index);
      })
      .catch((error) => {
        console.error(`加载分类 ${categoryId} 的数据失败:`, error);
        wx.hideLoading();
        wx.showToast({
          title: "加载失败，请重试",
          icon: "none",
        });
      });
  },

  // 预加载相邻分类的数据
  preloadAdjacentCategories(currentIndex) {
    const { categories } = this.data;

    // 预加载下一个分类（如果存在）
    if (currentIndex < categories.length - 1) {
      const nextCategory = categories[currentIndex + 1];
      this.preloadCategoryData(nextCategory.id);
    }

    // 预加载上一个分类（如果存在）
    if (currentIndex > 0) {
      const prevCategory = categories[currentIndex - 1];
      this.preloadCategoryData(prevCategory.id);
    }
  },

  // 预加载分类数据（不显示加载提示，在后台静默加载）
  preloadCategoryData(categoryId) {
    // 如果已经缓存，则不需要预加载
    if (this.data.categoryProducts[categoryId]) {
      return;
    }

    // 静默加载数据
    toolsDataManager
      .getToolsByCategory(categoryId)
      .then((products) => {
        // 数据管理器已处理图片缓存，直接使用
        // 缓存该分类的数据
        const categoryProducts = { ...this.data.categoryProducts };
        categoryProducts[categoryId] = products;

        // 更新缓存数据
        this.setData({ categoryProducts });
        console.log(`预加载分类 ${categoryId} 数据成功`);
      })
      .catch((error) => {
        console.error(`预加载分类 ${categoryId} 数据失败:`, error);
      });
  },

  // 定义跳转到工具详情页面的方法
  goToDetail(e) {
    // 从事件对象中获取触发事件的组件上绑定的自定义数据
    const index = e.currentTarget.dataset.index; // 获取被点击项在其列表中的索引
    // 根据索引从当前产品列表 (currentProducts) 中获取对应的工具对象
    const tool = this.data.currentProducts[index];
    // 在控制台打印被点击的产品信息，方便调试
    console.log("点击的产品:", tool);
    // 打印即将用于跳转的 tool.id
    console.log("准备跳转，产品 ID:", tool.id);
    // 使用微信小程序的导航 API 跳转到详情页面
    wx.navigateTo({
      // 指定目标页面的路径，并通过 URL 参数传递工具的 ID
      // 使用 encodeURIComponent 对 ID 进行编码，防止特殊字符引起问题
      url: `/pages/tool-detail/index?id=${encodeURIComponent(tool.id)}`,
    });
  },

  // 处理图片加载错误
  handleImageError(e) {
    const index = e.currentTarget.dataset.index;
    const productInfo = this.data.currentProducts[index];

    console.error(
      `图片加载失败，索引: ${index}，URL: ${productInfo.icon}，产品名称: ${productInfo.name}`
    );

    // 更新数据，使用默认图片
    const defaultIcon = "/images/placeholder/tool-icon.png";
    const newProducts = [...this.data.currentProducts];
    newProducts[index].icon = defaultIcon;

    this.setData({
      currentProducts: newProducts,
    });

    // 显示轻提示，便于调试
    wx.showToast({
      title: `图片加载失败: ${productInfo.name}`,
      icon: "none",
      duration: 1500,
    });
  },

  // 确保标签在可视区域内，如果不在则滚动使其可见
  ensureTabVisible(index) {
    const tabId = `tab-${index}`; // 使用 index 构建 ID
    const padding = 10; // 滚动到可见区域时的边距 (px)

    wx.nextTick(() => {
      const query = wx.createSelectorQuery().in(this);
      query
        .select(".tab-scroll")
        .fields({ rect: true, scrollOffset: true, size: true }); // 获取滚动视图信息
      query.select(`#${tabId}`).boundingClientRect(); // 获取目标标签信息
      query.exec((res) => {
        if (res && res.length >= 2 && res[0] && res[1]) {
          const scrollViewRect = res[0];
          const tabItemRect = res[1];
          const currentScrollLeft = scrollViewRect.scrollLeft;
          const maxScrollLeft =
            scrollViewRect.scrollWidth - scrollViewRect.width;

          // 判断目标标签是否完全可见 (考虑padding)
          const isTabVisible =
            tabItemRect.left >= padding &&
            tabItemRect.left + tabItemRect.width <=
              scrollViewRect.width - padding;

          console.log(
            `[ensureTabVisible] Index: ${index}, Tab ID: ${tabId}, Is Visible: ${isTabVisible}, Tab Left: ${tabItemRect.left}, Tab Width: ${tabItemRect.width}, ScrollView Width: ${scrollViewRect.width}, ScrollView ScrollWidth: ${scrollViewRect.scrollWidth}`
          );

          // 如果标签已经完全可见，则不滚动
          if (isTabVisible) {
            console.log(
              "[ensureTabVisible] Tab already visible, skipping scroll."
            );
            return;
          }

          // --- 如果标签不可见，计算使其可见的滚动距离 ---
          let targetScrollLeft = currentScrollLeft; // 默认为当前位置

          // 情况1：标签在左侧不可见
          if (tabItemRect.left < padding) {
            // 增加一个额外的左侧偏移量，让标签不紧贴左边界
            const leftOffset = 20; // 增加20px的左侧偏移
            targetScrollLeft =
              currentScrollLeft + tabItemRect.left - padding - leftOffset;
            console.log(
              `[ensureTabVisible] Tab is off-screen left. Calculating scroll with offset...`
            );
          }
          // 情况2：标签在右侧不可见
          else if (
            tabItemRect.left + tabItemRect.width >
            scrollViewRect.width - padding
          ) {
            targetScrollLeft =
              currentScrollLeft +
              (tabItemRect.left + tabItemRect.width - scrollViewRect.width) +
              padding;
            console.log(
              `[ensureTabVisible] Tab is off-screen right. Calculating scroll...`
            );
          }

          // 边界检查
          targetScrollLeft = Math.max(0, targetScrollLeft);
          targetScrollLeft = Math.min(targetScrollLeft, maxScrollLeft);

          console.log(
            `[ensureTabVisible] Calculated scroll: current=${currentScrollLeft}, target=${targetScrollLeft}, max=${maxScrollLeft}`
          );

          // 只有在计算出的滚动位置和当前位置显著不同时才更新
          if (
            isFinite(targetScrollLeft) &&
            Math.abs(targetScrollLeft - currentScrollLeft) > 1
          ) {
            this.setData({ tabScrollLeft: targetScrollLeft });
            console.log(
              `[ensureTabVisible] Setting tabScrollLeft to ${targetScrollLeft}`
            );
          } else if (isFinite(targetScrollLeft)) {
            console.log(
              `[ensureTabVisible] Scroll difference too small or target is same as current. Skipping setData.`
            );
          }
        } else {
          console.error("[ensureTabVisible] Failed to get rect info", res);
        }
      });
    });
  },
});
