/* index.wxss */
/* 设置页面的全局样式 */
page {
  background-color: #f6f6f6;
  /* 设置页面背景颜色为浅灰色 */
  height: 100%;
  /* 设置页面高度为视口高度的100% */
}

/* 主容器样式 */
.container {
  display: flex;
  /* 使用 Flexbox 布局 */
  flex-direction: column;
  /* 设置主轴方向为垂直方向 */
  height: 100%;
  /* 设置容器高度为父元素（page）的100% */
  box-sizing: border-box;
  /* 设置盒模型为 border-box，padding 和 border 不会增加元素总宽高 */
  padding: 0 0 30rpx 0;
  /* 设置容器的内边距，底部留出 30rpx */
}

/* 顶部可滑动标签栏容器样式 */
.tab-scroll-container {
  width: 100%;
  /* 容器宽度占满父元素 */
  height: 88rpx;
  /* 设置容器固定高度 */
  background-color: #ffffff;
  /* 设置背景颜色为白色 */
  position: sticky;
  /* 设置为粘性定位，使其在滚动时固定在顶部 */
  top: 0;
  /* 固定在距离视口顶部 0 的位置 */
  z-index: 100;
  /* 设置层叠顺序，确保在其他内容之上 */
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  /* 添加底部阴影效果 */
}

/* 可滑动标签栏本身样式 */
.tab-scroll {
  width: 100%;
  /* 宽度占满父容器 */
  height: 88rpx;
  /* 高度与父容器一致 */
  white-space: nowrap;
  /* 强制内部元素不换行，以便横向滚动 */
  /* 隐藏标准滚动条 (Firefox) */
  scrollbar-width: none;
  /* 隐藏标准滚动条 (IE and Edge) */
  -ms-overflow-style: none;
  overflow-x: auto;
  /* 允许内容在水平方向上滚动 */
  overflow-y: hidden;
  /* 禁止内容在垂直方向上滚动 */
  padding: 0 0 0 0rpx;
  /* 减少左侧内边距，让标签更靠左显示 */
}

/* 隐藏 WebKit 内核浏览器（如 Chrome, Safari）的滚动条 */
.tab-scroll::-webkit-scrollbar {
  display: none;
  /* 不显示滚动条 */
  width: 0;
  /* 设置滚动条宽度为0 */
  height: 0;
  /* 设置滚动条高度为0 */
}

/* 单个标签项样式 */
.tab-item {
  display: inline-block;
  /* 设置为行内块元素，使其可以在一行内排列并拥有宽高 */
  padding: 0 30rpx;
  /* 设置左右内边距，增加点击区域和视觉间距 */
  height: 88rpx;
  /* 设置高度与容器一致 */
  line-height: 88rpx;
  /* 设置行高与高度一致，实现文字垂直居中 */
  font-size: 26rpx;
  /* 设置字体大小 */
  color: #666;
  /* 设置默认文字颜色 */
  position: relative;
  /* 设置相对定位，为其伪元素定位提供基准 */
  transition: all 0.3s ease;
  /* 添加过渡效果，使变化更平滑 */
  margin: 0 2rpx;
  /* 添加轻微的左右外边距，防止缩放时挤压相邻元素 */
  transform-origin: center center;
  /* 修改变换原点为中心，减少抖动 */
}

/* 当前激活的标签项样式 */
.tab-item.active {
  color: #07c160;
  /* 设置激活状态下的文字颜色为绿色 */
  /* 移除字体加粗，减少文字宽度突变导致的抖动 */
  transform: scale(1.05);
  /* 添加轻微放大效果 */
  font-size: 26rpx;
  /* 保持字体大小一致 */
  text-shadow: 0 0 1px rgba(7, 193, 96, 0.2);
  /* 添加微妙的文字阴影，增强视觉效果 */
  transform-origin: center center;
  /* 变换原点为中心 */
}

/* 激活标签项下方的指示条样式 (使用伪元素 ::after) */
.tab-item.active::after {
  content: '';
  /* 伪元素必须设置 content 属性 */
  position: absolute;
  /* 设置绝对定位，相对于 .tab-item */
  bottom: 0;
  /* 定位在父元素底部 */
  left: 50%;
  /* 水平居中定位的起点 */
  transform: translateX(-50%);
  /* 通过 transform 实现精确水平居中 */
  width: 40rpx;
  /* 设置指示条宽度 */
  height: 6rpx;
  /* 设置指示条高度 */
  background-color: #07c160;
  /* 设置指示条背景颜色与激活文字颜色一致 */
  border-radius: 3rpx;
  /* 设置圆角 */
}

/* 滑动视图容器样式 */
.category-swiper {
  flex: 1;
  width: 100%;
  height: 100%;
  transition: all 0.3s ease;
}

/* 滑动视图项样式 */
.swiper-item {
  height: 100%;
  box-sizing: border-box;
  transition: transform 0.3s ease;
}

/* 激活中的swiper-item添加淡入效果 */
.swiper-item-active {
  animation: fadeIn 0.3s ease forwards;
}

/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0.8;
  }

  to {
    opacity: 1;
  }
}


/* 产品列表滚动区域样式 */
.products-scroll {
  height: 100%;
  box-sizing: border-box;
  padding: 20rpx;
}

/* 产品列表布局样式 (Flex 换行布局) */
.products-list {
  display: flex;
  /* 使用 Flexbox 布局 */
  flex-direction: row;
  /* 主轴方向为水平 */
  flex-wrap: wrap;
  /* 允许项目换行 */
  width: 100%;
  /* 宽度占满父容器 */
  justify-content: space-between;
  /* 使用负 margin 来抵消子元素 .product-item 的左右 margin，使得整体内容靠边 */
}

/* 单个产品项卡片样式 */
.product-item {
  width: calc(50% - 10rpx);
  /* 计算宽度，实现一行显示两个，减去左右 margin 的总和 (10rpx * 2) */
  /* 如果需要一行显示三个，可以使用 calc(33.333% - 20rpx) */
  margin: 10rpx 0;
  /* 设置外边距，提供卡片间的间距 */
  padding: 16rpx;
  /* 设置内边距 */
  background-color: #ffffff;
  /* 设置背景颜色为白色 */
  border-radius: 12rpx;
  /* 设置圆角 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  /* 添加轻微阴影效果 */
  display: flex;
  /* 使用 Flexbox 布局 */
  flex-direction: column;
  /* 设置主轴方向为垂直 */
  align-items: center;
  /* 交叉轴（水平）居中对齐 */
  box-sizing: border-box;
  /* 盒模型设置为 border-box */
  transition: all 0.3s ease;
  /* 为所有可过渡属性添加 0.3 秒的缓动效果 */
}

/* 产品项被点击时的交互样式 */
.product-item:active {
  transform: scale(0.98);
  /* 轻微缩小 */
  box-shadow: 0 0 4rpx rgba(0, 0, 0, 0.1);
  /* 阴影变淡变小 */
}

/* 产品图标样式 */
.product-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background-color: #f7f7f7;
  /* 添加背景色，图片加载前显示 */
}

.default-icon {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  /* 默认在底层 */
}

/* 当主图片加载失败时，显示默认图标 */
.product-icon[src='']+.default-icon,
.product-icon[src='/images/placeholder/tool-icon.png']+.default-icon {
  z-index: 1;
  /* 提升到上层 */
  display: block !important;
}

/* 产品名称样式 */
.product-name {
  font-size: 24rpx;
  /* 设置字体大小 */
  color: #333;
  /* 设置文字颜色 */
  text-align: center;
  /* 文字居中对齐 */
  width: 100%;
  /* 宽度占满父容器 */
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  white-space: nowrap;
  /* 强制文本不换行 */
}

/* 产品描述样式 (当前 WXML 中未使用，但保留样式) */
.product-desc {
  font-size: 24rpx;
  /* 设置字体大小 */
  color: #666;
  /* 设置文字颜色 */
  margin-top: 8rpx;
  /* 设置与上方元素的间距 */
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  white-space: nowrap;
  /* 强制文本不换行 */
}

/* 详情按钮样式 (当前 WXML 中未使用，但保留样式) */
.detail-btn {
  font-size: 24rpx;
  /* 设置字体大小 */
  color: white;
  /* 设置文字颜色为白色 */
  background-color: #07c160;
  /* 设置背景颜色为绿色 */
  border-radius: 8rpx;
  /* 设置圆角 */
  width: 180rpx;
  /* 设置固定宽度 */
  height: 60rpx;
  /* 设置固定高度 */
  line-height: 60rpx;
  /* 设置行高与高度一致，实现文字垂直居中 */
  margin: 0;
  /* 清除默认外边距 */
  padding: 0;
  /* 清除默认内边距 */
  margin-top: 16rpx;
  /* 设置与上方元素的间距 */
}

/* 移除按钮默认的边框样式 (针对微信小程序 button 组件的 ::after 伪元素) */
.detail-btn::after {
  border: none;
  /* 不显示边框 */
}

/* 调试信息容器样式 */
.debug-info {
  display: none;
  /* 默认隐藏调试信息 */
  /* 如果需要显示调试信息，可以改为 display: block; */
}

/* 调试信息内部文本样式 */
.debug-info text {
  display: block;
  /* 设置为块级元素，使其各占一行 */
  margin: 6rpx 0;
  /* 设置上下外边距 */
}

/* 导航说明样式 */
.nav-intro {
  width: 100%;
  padding: 16rpx 30rpx;
  background-color: #fffde7;
  text-align: center;
  box-sizing: border-box;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.nav-intro-text {
  font-size: 24rpx;
  color: #9e9d24;
  line-height: 1.5;
}

/* 移除滑动指示器样式 */
.swipe-indicator {
  display: none;
}

/* 产品列表区域容器样式 */
.products-container {
  flex: 1;
  width: 100%;
  box-sizing: border-box;
}

/* 滑动手势提示 */
.swipe-gesture-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 20rpx;
  padding: 20rpx 30rpx;
  animation: fadeInOut 3s ease-in-out forwards;
}

.hint-icon {
  width: 80rpx;
  height: 30rpx;
  margin-bottom: 10rpx;
  position: relative;
  overflow: hidden;
}

.hint-icon::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  background-color: white;
  animation: moveLeftRight 2s infinite ease-in-out;
}

.hint-text {
  color: white;
  font-size: 24rpx;
}

@keyframes moveLeftRight {
  0% {
    left: 0;
  }

  50% {
    left: calc(100% - 30rpx);
  }

  100% {
    left: 0;
  }
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
  }

  20% {
    opacity: 1;
  }

  80% {
    opacity: 1;
  }

  100% {
    opacity: 0;
    display: none;
  }
}

/* 隐藏产品列表滚动区域的滚动条 (WebKit) */
.products-scroll::-webkit-scrollbar {
  display: none;
  /* 恢复原状 */
  width: 0;
  /* 恢复原状 */
  height: 0;
  /* 恢复原状 */
  color: transparent;
}