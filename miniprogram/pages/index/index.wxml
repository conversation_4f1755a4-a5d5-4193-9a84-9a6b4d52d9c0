<!--pages/index/index.wxml-->
<!-- 页面主容器 -->
<view class="container">
  <!-- 顶部导航说明 -->
  <!-- <view class="nav-intro">
    <text class="nav-intro-text">本应用仅提供AI工具信息导航，不直接提供AI服务</text>
  </view> -->

  <!-- 顶部可滑动标签栏容器 -->
  <view class="tab-scroll-container">
    <!-- 可横向滚动的视图区域，用于承载分类标签 -->
    <scroll-view 
      class="tab-scroll" 
      scroll-x="true" 
      enable-flex="true" 
      scroll-left="{{tabScrollLeft}}" 
      show-scrollbar="{{false}}" 
      enhanced="{{true}}" 
      bounces="{{false}}">
      <!-- 循环渲染分类标签 -->
      <!-- tab项的基础样式类，并根据当前选中的tab (currentTab) 与当前项的索引 (index) 是否相等来动态添加 'active' 类 -->
      <!-- 使用 wx:for 遍历 categories 数组，为每个分类创建一个tab项 -->
      <!-- 指定循环项的唯一标识符为 item.id，优化渲染性能 -->
      <!-- 设置每个tab项的DOM ID，格式为 "tab-" 加上索引，用于 JS 查询 -->
      <!-- 将当前项的分类ID (item.id) 存储在 data-id 自定义属性中，方便在事件处理函数中获取 -->
      <!-- 将当前项在循环中的索引 (index) 存储在 data-index 自定义属性中 -->
      <!-- 绑定点击事件，当用户点击tab时，调用页面JS文件中的 switchTab 方法 -->
      <view 
        class="tab-item {{currentTab === index ? 'active' : ''}}" 
        wx:for="{{categories}}" 
        wx:key="id"
        id="tab-{{index}}"
        data-id="{{item.id}}"
        data-index="{{index}}"
        bindtap="switchTab">
        <!-- 显示分类的名称 -->
        <text>{{item.name}}</text>
      </view>
    </scroll-view>
  </view>
  
  <!-- 使用swiper组件实现左右滑动效果 -->
  <swiper 
    class="category-swiper" 
    current="{{currentTab}}" 
    bindchange="handleSwiperChange"
    duration="300"
    circular="{{false}}"
    easing-function="easeInOutCubic"
    indicator-dots="{{false}}"
    previous-margin="0"
    next-margin="0"
    display-multiple-items="1">
    
    <!-- 手势引导提示 (仅在首次加载时显示) -->
    <view class="swipe-gesture-hint" wx:if="{{showSwipeHint}}">
      <view class="hint-icon"></view>
      <view class="hint-text">左右滑动查看更多分类</view>
    </view>
    
    <!-- 为每个分类创建一个swiper-item -->
    <swiper-item 
      wx:for="{{categories}}" 
      wx:key="id" 
      class="swiper-item {{currentTab === index ? 'swiper-item-active' : ''}}">
      
      <!-- 产品列表容器 -->
      <scroll-view 
        scroll-y="true" 
        class="products-scroll"
        wx:if="{{index >= currentTab - 1 && index <= currentTab + 1}}">
        
        <!-- 产品列表 -->
        <view class="products-list">
          <view 
            wx:for="{{currentTab === index ? currentProducts : (categoryProducts[categories[index].id] || [])}}" 
            wx:for-index="productIndex"
            wx:for-item="product"
            wx:key="productIndex"
            class="product-item"
            data-index="{{productIndex}}"
            bindtap="goToDetail">
            
            <!-- 显示产品图标 -->
            <image 
              class="product-icon" 
              src="{{product.icon || '/images/placeholder/tool-icon.png'}}" 
              mode="aspectFill"
              lazy-load="true"
              binderror="handleImageError"
              data-index="{{productIndex}}">
            </image>
            
            <!-- 添加备用图片，默认隐藏，图片加载错误时显示 -->
            <image 
              class="product-icon default-icon" 
              src="/images/placeholder/tool-icon.png" 
              mode="aspectFill"
              wx:if="{{product.icon.indexOf('cloud://') === 0}}"
              style="display: none;">
            </image>
            
            <!-- 显示产品名称 -->
            <view class="product-name">{{product.name}}</view>
          </view>
        </view>
      </scroll-view>
    </swiper-item>
  </swiper>
</view>