<!-- pages/tool-detail/index.wxml -->
<view class="container">
  <block wx:if="{{loading}}">
    <view class="loading">
      <text>加载中...</text>
    </view>
  </block>
  <block wx:else>
    <!-- 顶部卡片 -->
    <view class="hero-section">
      <view class="tool-header">
        <view class="tool-icon">
          <image 
            src="{{tool.icon || '/images/placeholder/tool-icon.png'}}" 
            mode="aspectFill"
            lazy-load="true"
            binderror="handleImageError"></image>
        </view>
        <view class="tool-info">
          <view class="tool-name-row">
            <view class="tool-name">{{tool.name}}</view>
          </view>
          <!-- 使用tags字段显示标签 -->
          <view class="tool-labels" wx:if="{{tool.tags && tool.tags.length > 0}}">
            <view class="tool-tag" wx:for="{{tool.tags}}" wx:key="index">{{item}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 工具详情 -->
    <view class="detail-card">
      <view class="card-title">工具详情</view>
      
      <!-- 工具描述 -->
      <view class="detail-section">
        <view class="section-title">
          <text class="icon">📝</text>
          <text>工具描述</text>
        </view>
        <view class="section-content">
          <view class="tool-desc">{{tool.description}}</view>
        </view>
      </view>
      
      <!-- 访问地址 -->
      <view class="detail-section">
        <view class="section-title">
          <text class="icon">🔗</text>
          <text>访问地址</text>
        </view>
        <view class="section-content url-box">
          <text class="url">{{tool.url}}</text>
          <view class="copy-btn" bindtap="copyLink">复制</view>
        </view>
      </view>
    </view>

    <!-- 使用说明 -->
    <view class="detail-card">
      <view class="card-title">使用说明</view>
      <view class="usage-steps">
        <view class="step">
          <view class="step-number">1</view>
          <view class="step-content">点击"复制链接"按钮，并在浏览器中访问工具网站</view>
        </view>
        <view class="step">
          <view class="step-number">2</view>
          <view class="step-content">根据网站指引完成注册/登录（如需要）</view>
        </view>
        <view class="step">
          <view class="step-number">3</view>
          <view class="step-content">按照工具提示使用AI功能</view>
        </view>
      </view>
    </view>


  </block>
</view> 