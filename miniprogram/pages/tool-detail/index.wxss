/* pages/tool-detail/index.wxss */
.container {
    padding: 30rpx;
    background-color: #f7f8fa;
    min-height: 100vh;
    box-sizing: border-box;
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300rpx;
    color: #999;
    font-size: 28rpx;
}

/* 卡片通用样式 */
.hero-section,
.detail-card {
    background-color: #fff;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    margin-bottom: 30rpx;
    width: 100%;
    box-sizing: border-box;
}

/* 顶部卡片 */
.tool-header {
    display: flex;
    padding: 40rpx 30rpx;
}

.tool-icon {
    width: 120rpx;
    height: 120rpx;
    border-radius: 24rpx;
    overflow: hidden;
    margin-right: 30rpx;
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
    background-color: #f0f0f0;
}

.tool-icon image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.tool-info {
    flex: 1;
}

.tool-name-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16rpx;
}

.tool-name {
    font-size: 40rpx;
    font-weight: bold;
    color: #333;
}

/* 标签样式 */
.tool-labels {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 16rpx;
}

.tool-tag {
    font-size: 24rpx;
    padding: 6rpx 18rpx;
    border-radius: 12rpx;
    margin-right: 16rpx;
    margin-bottom: 8rpx;
    color: #555;
    background-color: #e6f2ff;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

/* 为不同标签设置不同颜色 */
.tool-tag:nth-child(1) {
    background-color: #e6f2ff;
    /* 浅蓝色 */
    color: #4080c0;
}

.tool-tag:nth-child(2) {
    background-color: #fff2e6;
    /* 浅橙色 */
    color: #e67300;
}

.tool-tag:nth-child(3) {
    background-color: #e6ffe6;
    /* 浅绿色 */
    color: #2d862d;
}

.tool-tag:nth-child(4) {
    background-color: #f2e6ff;
    /* 浅紫色 */
    color: #8c4db8;
}

.tool-tag:nth-child(5) {
    background-color: #ffe6e6;
    /* 浅红色 */
    color: #cc3333;
}

/* 原有样式保持不变 */
.tool-category.hot {
    background-color: #ffebee;
    color: #f44336;
}

.tool-category.search {
    background-color: #e3f2fd;
    color: #2196f3;
}

.tool-category.text {
    background-color: #e8f5e9;
    color: #4caf50;
}

.tool-category.image {
    background-color: #ede7f6;
    color: #673ab7;
}

.tool-label {
    font-size: 24rpx;
    padding: 4rpx 16rpx;
    border-radius: 12rpx;
}

.tool-label.hot {
    background-color: #fff8e1;
    color: #ffc107;
}

.tool-label.recommend {
    background-color: #e8f5e9;
    color: #4caf50;
}

.tool-desc {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
}

/* 卡片标题统一样式 */
.card-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
    padding: 30rpx 30rpx 20rpx 30rpx;
    border-bottom: 1px solid #f0f0f0;
}

/* 详情部分内容padding统一 */
.detail-section {
    margin-bottom: 30rpx;
    padding-left: 30rpx;
    padding-right: 30rpx;
}

.detail-section:last-child {
    margin-bottom: 0;
    padding-bottom: 30rpx;
}

.section-title {
    display: flex;
    align-items: center;
    font-size: 30rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 16rpx;
}

.section-title .icon {
    margin-right: 12rpx;
    font-size: 32rpx;
}

.section-content {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
}

/* URL 样式 */
.url-box {
    display: flex;
    align-items: center;
    background-color: #f7f8fa;
    padding: 20rpx;
    border-radius: 12rpx;
}

.url {
    flex: 1;
    word-break: break-all;
    color: #2196f3;
}

.copy-btn {
    margin-left: 20rpx;
    padding: 8rpx 20rpx;
    background-color: #2196f3;
    color: #fff;
    border-radius: 30rpx;
    font-size: 24rpx;
}

/* 适用场景标签 */
.scene-list {
    display: flex;
    flex-wrap: wrap;
}

.scene-tag {
    padding: 12rpx 24rpx;
    background-color: #f7f8fa;
    color: #666;
    border-radius: 30rpx;
    margin-right: 16rpx;
    margin-bottom: 16rpx;
    font-size: 26rpx;
}

/* 评分样式 */
.rating-container {
    display: flex;
    align-items: center;
}

.rating-stars {
    display: flex;
}

.star {
    font-size: 36rpx;
    color: #e0e0e0;
    margin-right: 6rpx;
}

.star.active {
    color: #ffc107;
}

.star.half-active {
    position: relative;
    color: #e0e0e0;
}

.star.half-active::after {
    content: '★';
    position: absolute;
    left: 0;
    top: 0;
    width: 50%;
    overflow: hidden;
    color: #ffc107;
}

.rating-text {
    margin-left: 16rpx;
    color: #ffc107;
    font-weight: bold;
}

/* 使用步骤 */
.usage-steps {
    padding: 10rpx 30rpx 30rpx 30rpx;
}

.step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 24rpx;
}

.step:last-child {
    margin-bottom: 0;
}

.step-number {
    width: 48rpx;
    height: 48rpx;
    border-radius: 24rpx;
    background-color: #2196f3;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    margin-right: 20rpx;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
    font-size: 28rpx;
    color: #666;
    padding-top: 6rpx;
}

/* 底部按钮 */
.bottom-actions {
    display: flex;
    position: sticky;
    bottom: 30rpx;
    z-index: 10;
}

.action-btn {
    flex: 1;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 15rpx;
}

.action-btn.copy {
    background-color: #f0f0f0;
    color: #666;
}

.action-btn.open {
    background-color: #2196f3;
    color: #fff;
    box-shadow: 0 4rpx 12rpx rgba(33, 150, 243, 0.3);
}

/* 免责声明样式 */
.disclaimer-card {
    border: 1px solid #e0e0e0;
    background-color: #fffde7;
}

.disclaimer-content {
    padding: 0 30rpx 30rpx 30rpx;
}

.disclaimer-content text {
    color: #757575;
    font-size: 26rpx;
    line-height: 1.6;
}