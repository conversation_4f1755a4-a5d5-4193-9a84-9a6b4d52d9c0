// 使用数据管理器工厂获取工具数据
const dataManagerFactory = require("../../utils/dataManagerFactory");
const toolsDataManager = dataManagerFactory.getDataManager();
const config = require("../../config/config");

Page({
  data: {
    tool: null,
    loading: true,
    defaultIcon: "/images/placeholder/tool-icon.png",
    dataSourceType: "", // 当前使用的数据源类型
  },

  onLoad(options) {
    // 从 options 中获取编码后的 id
    const encodedId = options.id;
    // 对 id 进行解码
    const id = decodeURIComponent(encodedId);

    // 设置数据源类型
    const dataSourceType = dataManagerFactory.getDataSourceType();
    this.setData({ dataSourceType });
    console.log(`工具详情页数据源类型: ${dataSourceType}`);

    console.log("[ToolDetail] onLoad: 收到的原始 id:", encodedId);
    console.log("[ToolDetail] onLoad: 解码后的 id:", id);

    // 使用解码后的 id 获取工具详情
    this.getToolById(id);
  },

  getToolById(id) {
    this.setData({
      loading: true,
      tool: null,
    });

    toolsDataManager
      .getToolById(id)
      .then((fetchedTool) => {
        if (!fetchedTool) {
          wx.showToast({ title: "未找到对应工具", icon: "none" });
          this.setData({ loading: false });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
          return;
        }

        // 根据数据源类型处理图片路径
        if (this.data.dataSourceType === "cloud") {
          // 云数据源处理方式
          this.processCloudImage(fetchedTool, id);
        } else {
          // 本地数据源直接使用
          console.log(
            `[ToolDetail] 使用本地数据源，工具 ID: ${id}。图标路径: ${fetchedTool.icon}`
          );
          this.setData({
            tool: fetchedTool,
            loading: false,
          });
        }
      })
      .catch((error) => {
        console.error("获取工具详情失败:", error);
        wx.showToast({ title: "获取工具详情失败", icon: "none" });
        this.setData({ loading: false });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      });
  },

  // 处理云存储图片
  processCloudImage(fetchedTool, id) {
    const cacheKey = `icon_cache_${id}`;
    const cachedIconPath = wx.getStorageSync(cacheKey);

    // 1. 检查缓存
    if (cachedIconPath) {
      console.log(
        `[工具详情] 图标缓存命中，工具 ID: ${id}。使用本地路径: ${cachedIconPath}`
      );
      fetchedTool.icon = cachedIconPath;
      this.setData({ tool: fetchedTool, loading: false });
      // 2. 无缓存，且是云存储路径，则获取临时链接并尝试缓存
    } else if (fetchedTool.icon && fetchedTool.icon.startsWith("cloud://")) {
      console.log(
        `[ToolDetail] No cache found for ${id}. Fetching temp URL for ${fetchedTool.icon}`
      );
      const originalCloudPath = fetchedTool.icon; // 保留原始路径

      this.getCloudImageTempUrl(originalCloudPath)
        .then((tempUrl) => {
          if (tempUrl) {
            console.log(`[ToolDetail] Got temp URL for ${id}: ${tempUrl}`);
            // 使用临时链接进行本次显示
            fetchedTool.icon = tempUrl;
            this.setData({ tool: fetchedTool, loading: false });
            // 异步缓存图标
            this.cacheIconAsync(id, tempUrl);
          } else {
            // 云函数调用成功但未返回有效 URL
            console.error(
              `[ToolDetail] getCloudImageTempUrl returned null for ${id}`
            );
            fetchedTool.icon = this.data.defaultIcon;
            this.setData({ tool: fetchedTool, loading: false });
          }
        })
        .catch((err) => {
          // 云函数调用失败
          console.error(`[ToolDetail] Failed to get temp URL for ${id}:`, err);
          fetchedTool.icon = this.data.defaultIcon;
          this.setData({ tool: fetchedTool, loading: false });
        });
      // 3. 非云存储路径或无图标路径，使用原始或默认图标
    } else {
      console.log(
        `[ToolDetail] Icon for ${id} is not cloud path or missing. Using default/original.`
      );
      fetchedTool.icon = fetchedTool.icon || this.data.defaultIcon;
      this.setData({ tool: fetchedTool, loading: false });
    }
  },

  // 新增：异步缓存图标到本地存储
  cacheIconAsync(id, tempUrl) {
    try {
      const cacheKey = `tool_icon_${id}`;
      // 设置缓存，可以考虑添加过期时间
      wx.setStorageSync(cacheKey, tempUrl);
      console.log(
        `[ToolDetail] Icon for ${id} cached successfully: ${cacheKey}`
      );
    } catch (e) {
      console.error(`[ToolDetail] Failed to cache icon for ${id}:`, e);
    }
  },

  // 获取云存储图片的临时URL (修改为调用云函数)
  getCloudImageTempUrl(fileId) {
    return new Promise((resolve, reject) => {
      // 移除修正 File ID 的逻辑
      const correctedFileId = fileId; // 直接使用传入的 fileId

      // 使用 fileId 调用云函数
      console.log(
        `[ToolDetail] 开始通过云函数获取图片临时 URL: ${correctedFileId}`
      );
      wx.cloud.callFunction({
        name: "getJsonData", // 调用与首页相同的云函数
        data: {
          operation: "getIcons", // 指定操作为获取图标
          iconPaths: [correctedFileId], // 将修正后的 fileId 包装成数组传递
        },
        success: (res) => {
          console.log("[ToolDetail] 云函数获取图片临时URL成功:", res);

          if (
            res.result &&
            res.result.success &&
            res.result.data &&
            res.result.data.length > 0
          ) {
            const fileInfo = res.result.data[0];
            // 注意：这里比较时也应该用修正后的 ID
            if (
              fileInfo.fileID === correctedFileId && // 使用修正后的 ID 比较
              fileInfo.tempFileURL &&
              fileInfo.status === 0
            ) {
              console.log(
                `[ToolDetail] 成功解析到临时 URL: ${fileInfo.tempFileURL}`
              );
              resolve(fileInfo.tempFileURL);
            } else {
              // 日志中打印原始 fileId 和 修正后的 fileId 可能更有帮助
              console.error(
                `[ToolDetail] 云函数返回数据解析失败或状态错误: status=${fileInfo.status}, requestedFileID=${correctedFileId}, returnedFileID=${fileInfo.fileID}`
              );
              resolve(null); // 解析失败，返回 null
            }
          } else {
            console.error("[ToolDetail] 云函数返回数据格式错误:", res.result);
            resolve(null); // 格式错误，返回 null
          }
        },
        fail: (err) => {
          console.error("[ToolDetail] 调用云函数 getIcons 失败:", err);
          reject(err); // 云函数调用失败，reject Promise
        },
      });
    });
  },

  // 复制链接
  copyLink() {
    const { url } = this.data.tool;
    wx.setClipboardData({
      data: url,
      success: () => {
        wx.showToast({
          title: "链接已复制",
          icon: "success",
        });
      },
    });
  },

  // 跳转到工具网站
  openWebview() {
    const { url } = this.data.tool;
    wx.navigateTo({
      url: `/pages/web/index?url=${encodeURIComponent(url)}`,
    });
  },

  // 修改：处理图片加载错误
  handleImageError() {
    if (this.data.tool) {
      const currentIcon = this.data.tool.icon;
      const defaultIcon = this.data.defaultIcon;
      const toolId = this.data.tool.id;

      console.error(
        `[ToolDetail] Image load failed for tool ${toolId}. Failed URL: ${currentIcon}`
      );

      // 设置默认图标（如果当前不是默认图标）
      if (currentIcon !== defaultIcon) {
        console.log(`[ToolDetail] Setting default icon for ${toolId}`);
        this.setData({
          "tool.icon": defaultIcon,
        });
      } else {
        console.warn(
          `[ToolDetail] Default icon itself failed to load or was already set: ${defaultIcon}`
        );
      }
    } else {
      console.error(
        "[ToolDetail] handleImageError called but tool data is missing."
      );
    }
  },
});
