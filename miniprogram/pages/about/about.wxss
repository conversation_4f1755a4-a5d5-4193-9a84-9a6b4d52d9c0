/* miniprogram/pages/about/about.wxss */
.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40rpx;
    box-sizing: border-box;
    background-color: #f8f8f8;
    min-height: 100vh;
}

.logo {
    width: 160rpx;
    height: 160rpx;
    margin-bottom: 20rpx;
    border-radius: 50%;
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.app-name {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
}

/*
.version {
    font-size: 24rpx;
    color: #999;
    margin-bottom: 40rpx;
}
*/

.section {
    width: 100%;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section .title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    padding-bottom: 10rpx;
    border-bottom: 1rpx solid #eee;
}

.section .description,
.section .important-notice,
.section .vision,
.section .email {
    display: block;
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 15rpx;
}

.important-notice {
    color: #e64340;
    /* 红色强调 */
    font-weight: bold;
}

.email {
    color: #007aff;
    /* 蓝色，表示可交互 */
    word-break: break-all;
}

.remark-tip {
    font-size: 26rpx;
    /* 稍微小一点 */
    color: #999;
    /* 灰色，不那么显眼 */
    margin-top: -10rpx;
    /* 减少与上一行间距 */
    margin-bottom: 15rpx;
    /* 保持与下方间距 */
}

.copyright {
    margin-top: auto;
    /* 将版权信息推到底部 */
    padding-top: 40rpx;
    text-align: center;
    font-size: 24rpx;
    color: #aaa;
}

.icp-info {
    padding-top: 10rpx;
    /* 与版权信息间距 */
    text-align: center;
    font-size: 24rpx;
    color: #aaa;
}

.wechat-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    /* 让微信号和按钮分开 */
    background-color: #f0f0f0;
    /* 轻微背景色区分 */
    padding: 15rpx 20rpx;
    border-radius: 8rpx;
    margin-top: 10rpx;
}

.wechat-id {
    font-size: 28rpx;
    color: #555;
    word-break: break-all;
    /* 防止长 ID 溢出 */
}

.copy-btn {
    font-size: 26rpx;
    color: #007aff;
    background-color: #e0e0e0;
    padding: 8rpx 16rpx;
    border-radius: 6rpx;
    margin-left: 20rpx;
    /* 与微信号保持距离 */
    cursor: pointer;
    /* 桌面端显示手型 */
}

.copy-btn:active {
    /* 点击效果 */
    background-color: #d0d0d0;
}

.qr-hint {
    display: block;
    /* 独占一行 */
    text-align: center;
    /* 居中显示 */
    font-size: 24rpx;
    /* 稍小字体 */
    color: #999;
    /* 灰色 */
    margin-top: 20rpx;
    /* 与上方元素间距 */
}

.wechat-qr {
    display: block;
    /* 确保图片独占一行 */
    width: 300rpx;
    /* 设置合适的宽度 */
    margin: 20rpx auto 0;
    /* 上方留空，左右居中 */
    border-radius: 8rpx;
    /* 轻微圆角 */
}