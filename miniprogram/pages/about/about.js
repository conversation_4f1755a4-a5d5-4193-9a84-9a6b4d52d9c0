Page({
  data: {
    // version: "1.0.0", // 暂时硬编码版本号
    year: new Date().getFullYear(),
    wechatID: "pialio", // 请替换为你的微信号
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 可以在这里尝试动态获取版本号，例如从 app.js 或其他配置中读取
    // const accountInfo = wx.getAccountInfoSync();
    // this.setData({
    //   version: accountInfo.miniProgram.version
    // });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},

  copyWechatID() {
    wx.setClipboardData({
      data: this.data.wechatID,
      success: () => {
        wx.showToast({
          title: "微信号已复制",
          icon: "success",
          duration: 1500,
        });
      },
      fail: () => {
        wx.showToast({
          title: "复制失败",
          icon: "none",
          duration: 1500,
        });
      },
    });
  },
});
