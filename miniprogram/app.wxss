/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

/* 占位图样式 */
.placeholder-icon {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 12rpx;
  color: #999;
}

.placeholder-text {
  font-size: 24rpx;
  color: #999;
}

button {
  background: initial;
}

button:focus {
  outline: 0;
}

button::after {
  border: none;
}


page {
  background: #f6f6f6;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}