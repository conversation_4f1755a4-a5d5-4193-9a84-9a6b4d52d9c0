/**
 * 云函数数据管理器
 * 通过云函数从云存储获取AI工具数据
 */

// 引入配置
const config = require("../config/config");
const { DATA_VERSION } = config;
// 引入图片缓存管理器
const imageCacheManager = require("./imageCacheManager");

// 获取云环境ID
const app = getApp();
const ENV_ID = app ? app.getCloudEnvId() : config.DATA_SOURCE.CLOUD.ENV_ID;

// 内存中的数据缓存
let memoryCache = null;

// 数据版本日期
const DATA_VERSION_DATE = DATA_VERSION.DATE;

// JSON文件在云存储中的路径 - 使用基于版本日期的路径
const TOOLS_JSON_PATH = config.DATA_SOURCE.CLOUD.TOOLS_JSON_PATH;
const IMAGES_BASE_PATH = config.DATA_SOURCE.CLOUD.IMAGES_BASE_PATH;

console.log(`云函数数据管理器初始化，使用数据版本: ${DATA_VERSION_DATE}`);
console.log(`工具JSON路径: ${TOOLS_JSON_PATH}`);
console.log(`图片基础路径: ${IMAGES_BASE_PATH}`);

/**
 * 初始化工具数据
 * @returns {Promise<boolean>} 是否初始化成功
 */
function initToolsData() {
  console.log(
    `开始通过云函数加载JSON数据，版本: ${DATA_VERSION_DATE}, 环境ID: ${ENV_ID}...`
  );

  return new Promise((resolve, reject) => {
    if (memoryCache) {
      console.log("使用内存缓存数据");
      resolve(true);
      return;
    }

    fetchCloudData()
      .then((success) => {
        resolve(success);
      })
      .catch((error) => {
        console.error(
          `通过云函数获取JSON数据失败，版本: ${DATA_VERSION_DATE}:`,
          error
        );
        reject(error);
      });
  });
}

/**
 * 通过云函数从云存储获取JSON数据
 * @returns {Promise<boolean>} 是否获取成功
 */
async function fetchCloudData() {
  try {
    console.log(`正在调用云函数获取JSON数据，版本: ${DATA_VERSION_DATE}...`);

    // 调用云函数获取JSON数据
    const result = await wx.cloud.callFunction({
      name: "getJsonData",
      data: {
        filePath: TOOLS_JSON_PATH,
      },
    });

    console.log("🔍 云函数完整返回结果:", JSON.stringify(result, null, 2));

    if (!result || !result.result) {
      console.error(`❌ 云函数返回结果无效，版本: ${DATA_VERSION_DATE}`);
      console.error("完整result:", result);
      return false;
    }

    if (!result.result.success) {
      console.error(`❌ 云函数返回错误，版本: ${DATA_VERSION_DATE}`);
      console.error("错误信息:", result.result.error || "未提供错误信息");
      console.error("调试数据:", result.result.debug || "无调试数据");
      console.error(
        "完整result.result:",
        JSON.stringify(result.result, null, 2)
      );
      return false;
    }

    const data = result.result.data;
    console.log(`✅ 成功获取JSON数据，版本: ${DATA_VERSION_DATE}`);

    if (
      !data.categories ||
      !data.tools ||
      !Array.isArray(data.categories) ||
      !Array.isArray(data.tools)
    ) {
      console.error("❌ JSON数据格式不正确");
      console.error("数据结构:", {
        hasCategories: !!data.categories,
        categoriesIsArray: Array.isArray(data.categories),
        categoriesLength: data.categories?.length,
        hasTools: !!data.tools,
        toolsIsArray: Array.isArray(data.tools),
        toolsLength: data.tools?.length,
      });
      return false;
    }

    // 处理数据为所需格式
    const processedData = await processData(data.categories, data.tools);

    if (!processedData) {
      console.error("❌ 数据处理失败");
      return false;
    }

    // 更新内存缓存
    memoryCache = processedData;
    console.log(`✅ 数据加载完成，版本: ${DATA_VERSION_DATE}`);

    return true;
  } catch (error) {
    console.error(
      `❌ 调用云函数获取数据失败，版本: ${DATA_VERSION_DATE}:`,
      error
    );
    console.error("错误详情:", {
      name: error.name,
      message: error.message,
      stack: error.stack,
    });
    return false;
  }
}

/**
 * 处理数据为所需格式，并集成图片缓存
 * @param {Array} categories - 分类数据
 * @param {Array} tools - 工具数据
 * @returns {Object} 处理后的数据
 */
async function processData(categories, tools) {
  try {
    if (!Array.isArray(categories) || !Array.isArray(tools)) {
      console.error("数据格式不正确");
      return null;
    }

    console.log(`[CloudManager] 开始处理数据，工具数量: ${tools.length}`);

    // 第一步：处理云存储路径格式，将旧路径转换为新版本路径
    const processedTools = tools.map((tool) => {
      if (tool.icon && tool.icon.indexOf("cloud://") === 0) {
        // 检查是否是旧的 ai_tools/images 路径格式
        if (tool.icon.indexOf("/ai_tools/images/") !== -1) {
          // 将 ai_tools/images 替换为当前版本的数据路径
          const newIcon = tool.icon.replace(
            "/ai_tools/images/",
            `/${IMAGES_BASE_PATH}/`
          );
          console.log(`图标路径转换: ${tool.name}`);
          console.log(`  原路径: ${tool.icon}`);
          console.log(`  新路径: ${newIcon}`);
          tool.icon = newIcon;
        } else if (tool.icon.indexOf(`/数据${DATA_VERSION_DATE}/`) !== -1) {
          // 如果已经是正确的版本路径，保持不变
          console.log(`图标路径已正确: ${tool.name} -> ${tool.icon}`);
        } else {
          // 其他情况，尝试构建正确的路径
          const iconPathParts = tool.icon.split("/");
          const fileName = iconPathParts[iconPathParts.length - 1];
          const category = tool.categoryId || "未分类";

          const newIcon = `cloud://${ENV_ID}.636c-${ENV_ID}-1349397796/${IMAGES_BASE_PATH}/${category}/${fileName}`;
          console.log(`图标路径重建: ${tool.name}`);
          console.log(`  原路径: ${tool.icon}`);
          console.log(`  新路径: ${newIcon}`);
          tool.icon = newIcon;
        }
      } else if ((!tool.icon || tool.icon === "") && tool.localIcon) {
        // 处理icon为空但localIcon有值的情况
        const newIcon = `cloud://${ENV_ID}.636c-${ENV_ID}-1349397796/数据${DATA_VERSION_DATE}/${tool.localIcon}`;
        console.log(`从localIcon构建图标路径: ${tool.name}`);
        console.log(`  localIcon: ${tool.localIcon}`);
        console.log(`  新路径: ${newIcon}`);
        tool.icon = newIcon;
      } else if (!tool.icon || tool.icon === "") {
        // 如果既没有icon也没有localIcon，记录警告
        console.warn(`工具 ${tool.name} 缺少图标信息`);
      }
      return tool;
    });

    // 第二步：获取云存储图片的临时URL
    const toolsWithTempUrls = await getCloudImageTempUrls(processedTools);

    // 第三步：使用图片缓存管理器处理图片
    const finalTools = await processBatchImagesWithCache(toolsWithTempUrls);

    // 按分类组织工具数据
    const toolsData = {};

    categories.forEach((category) => {
      const categoryId = category.id;
      // 找出属于该分类的所有工具
      toolsData[categoryId] = finalTools.filter(
        (tool) => tool.categoryId === categoryId
      );
    });

    console.log(`数据处理完成，版本: ${DATA_VERSION_DATE}, 信息汇总:`);
    console.log("- 分类数量:", categories.length);
    console.log("- 总工具数量:", finalTools.length);

    return {
      categories,
      toolsData,
      allTools: finalTools,
    };
  } catch (error) {
    console.error("处理数据出错:", error);
    return null;
  }
}

/**
 * 获取云存储图片的临时访问URL
 * @param {Array} tools - 工具数据数组
 * @returns {Array} 处理后的工具数据
 */
async function getCloudImageTempUrls(tools) {
  console.log(`[CloudManager] 开始获取云存储图片临时URL`);

  // 获取所有需要处理的云文件ID
  const cloudPaths = tools
    .filter((tool) => tool.icon && tool.icon.indexOf("cloud://") === 0)
    .map((tool) => tool.icon);

  if (cloudPaths.length === 0) {
    console.log("没有需要处理的云存储图片");
    return tools;
  }

  console.log(`需要处理的云存储图片数量: ${cloudPaths.length}`);
  console.log("前3个图标路径示例:", cloudPaths.slice(0, 3));

  try {
    // 使用云函数获取临时访问URL
    console.log("正在调用云函数获取图标临时URL...");
    const result = await wx.cloud.callFunction({
      name: "getJsonData",
      data: {
        operation: "getIcons",
        iconPaths: cloudPaths,
      },
    });

    console.log("🔍 图标云函数返回结果:", JSON.stringify(result, null, 2));

    if (!result.result || !result.result.success || !result.result.data) {
      console.error("❌ 图标云函数返回错误:");
      console.error("- success:", result.result?.success);
      console.error("- error:", result.result?.error);
      console.error("- debug:", result.result?.debug);
      console.error("完整result:", result);

      // 为所有云存储图片设置默认图片
      return tools.map((tool) => {
        if (tool.icon && tool.icon.indexOf("cloud://") === 0) {
          console.log(`设置默认图标: ${tool.name}`);
          tool.icon = "/images/placeholder/tool-icon.png";
        }
        return tool;
      });
    }

    // 更新产品图标为临时URL
    const fileList = result.result.data || [];
    console.log(`✅ 图标云函数成功，返回${fileList.length}个文件结果`);

    // 创建fileID到tempFileURL的映射
    const fileIdToUrl = {};
    fileList.forEach((file, index) => {
      if (file.fileID && file.tempFileURL && file.status === 0) {
        fileIdToUrl[file.fileID] = file.tempFileURL;
        console.log(
          `图标[${index}] 成功: ${file.fileID} -> ${file.tempFileURL}`
        );
      } else if (file.status !== 0) {
        console.error(
          `图标[${index}] 失败: ${file.fileID}, 状态: ${file.status}, 错误: ${
            file.errMsg || "未知错误"
          }`
        );
      }
    });

    console.log(`有效图标URL映射数量: ${Object.keys(fileIdToUrl).length}`);

    // 更新工具的icon URL
    const processedTools = tools.map((tool) => {
      if (tool.icon && tool.icon.indexOf("cloud://") === 0) {
        const tempUrl = fileIdToUrl[tool.icon];
        if (tempUrl) {
          console.log(
            `✅ 成功获取临时URL: ${tool.name} -> ${tempUrl.substring(0, 80)}...`
          );
          tool.icon = tempUrl;
          tool.originalCloudPath = tool.icon; // 保存原始云路径
        } else {
          console.error(
            `❌ 无法获取临时URL: ${tool.name}, 文件ID: ${tool.icon}`
          );
          tool.icon = "/images/placeholder/tool-icon.png";
        }
      }
      return tool;
    });

    console.log(`[CloudManager] 临时URL获取完成`);
    return processedTools;
  } catch (error) {
    console.error("❌ 调用云函数获取图片临时URL失败:", error);
    console.error("错误详情:", {
      name: error.name,
      message: error.message,
      stack: error.stack,
    });

    // 为所有云存储图片设置默认图片
    return tools.map((tool) => {
      if (tool.icon && tool.icon.indexOf("cloud://") === 0) {
        tool.icon = "/images/placeholder/tool-icon.png";
      }
      return tool;
    });
  }
}

/**
 * 使用图片缓存管理器批量处理图片
 * @param {Array} tools - 工具数据数组
 * @returns {Array} 处理后的工具数据
 */
async function processBatchImagesWithCache(tools) {
  console.log(`[CloudManager] 开始使用缓存管理器处理图片`);

  try {
    // 批量处理所有图片URL
    const imageUrls = tools.map((tool) => tool.icon);
    const cachedImagePaths = await imageCacheManager.getBatchImages(imageUrls);

    // 更新工具数据中的图片路径
    const processedTools = tools.map((tool, index) => {
      const cachedPath = cachedImagePaths[index];
      if (cachedPath && cachedPath !== tool.icon) {
        console.log(
          `[CloudManager] 使用缓存图片: ${tool.name} -> ${cachedPath}`
        );
        tool.icon = cachedPath;
        tool.isCached = true;
      }
      return tool;
    });

    console.log(`[CloudManager] 图片缓存处理完成`);

    // 定期清理缓存大小
    setTimeout(() => {
      imageCacheManager.manageCacheSize();
    }, 1000);

    return processedTools;
  } catch (error) {
    console.error("[CloudManager] 批量处理图片缓存失败:", error);
    return tools;
  }
}

/**
 * 获取所有工具分类
 * @returns {Promise<Array>} 分类列表
 */
function getCategories() {
  console.log("获取分类数据...");

  return new Promise((resolve, reject) => {
    initToolsData()
      .then(() => {
        if (!memoryCache || !memoryCache.categories) {
          console.error("分类数据不可用，memoryCache:", memoryCache);
          reject(new Error("分类数据不可用"));
          return;
        }
        console.log("返回分类数据，数量:", memoryCache.categories.length);
        resolve(memoryCache.categories);
      })
      .catch(reject);
  });
}

/**
 * 根据分类ID获取工具列表
 * @param {string} categoryId - 分类ID
 * @returns {Promise<Array>} 工具列表
 */
function getToolsByCategory(categoryId) {
  console.log(`获取分类 ${categoryId} 的工具数据...`);

  return new Promise((resolve, reject) => {
    initToolsData()
      .then(() => {
        console.log("数据初始化完成，开始获取指定分类工具");
        const tools = getToolsByCategoryFromCache(categoryId);
        console.log(`返回分类 ${categoryId} 的工具数据，数量:`, tools.length);
        if (tools.length > 0) {
          console.log("工具示例:", tools[0]);
        }
        resolve(tools);
      })
      .catch((error) => {
        console.error(`获取分类 ${categoryId} 工具数据失败:`, error);
        reject(error);
      });
  });
}

/**
 * 从缓存中获取指定分类的工具列表
 * @param {string} categoryId - 分类ID
 * @returns {Array} 工具列表
 */
function getToolsByCategoryFromCache(categoryId) {
  if (!memoryCache || !memoryCache.toolsData) {
    console.error("工具数据不可用，memoryCache:", memoryCache);
    return [];
  }

  const tools = memoryCache.toolsData[categoryId] || [];
  console.log(`从缓存中获取分类 ${categoryId} 的工具，找到 ${tools.length} 个`);
  return tools;
}

/**
 * 获取所有工具数据
 * @returns {Promise<Array>} 所有工具列表
 */
function getAllToolsData() {
  console.log("获取所有工具数据...");

  return new Promise((resolve, reject) => {
    initToolsData()
      .then(() => {
        if (!memoryCache || !memoryCache.allTools) {
          console.error("工具数据不可用，memoryCache:", memoryCache);
          reject(new Error("工具数据不可用"));
          return;
        }
        console.log("返回所有工具数据，数量:", memoryCache.allTools.length);
        resolve(memoryCache.allTools);
      })
      .catch(reject);
  });
}

/**
 * 根据工具ID获取工具详情
 * @param {string} toolId - 工具ID
 * @returns {Promise<Object>} 工具详情
 */
function getToolById(toolId) {
  console.log(`获取工具 ${toolId} 的详情...`);

  return new Promise((resolve, reject) => {
    initToolsData()
      .then(() => {
        if (!memoryCache || !memoryCache.allTools) {
          console.error("工具数据不可用，memoryCache:", memoryCache);
          reject(new Error("工具数据不可用"));
          return;
        }

        // 根据工具ID查找工具详情
        const tool = memoryCache.allTools.find((item) => item.id === toolId);

        if (!tool) {
          console.error(`未找到ID为 ${toolId} 的工具`);
          reject(new Error(`未找到工具: ${toolId}`));
          return;
        }

        console.log(`返回工具 ${toolId} 的详情:`, tool);
        resolve(tool);
      })
      .catch(reject);
  });
}

/**
 * 获取图片缓存统计信息
 * @returns {Object} 缓存统计
 */
function getImageCacheStats() {
  return imageCacheManager.getCacheStats();
}

/**
 * 清理图片缓存
 * @returns {Promise} 清理结果
 */
function clearImageCache() {
  return imageCacheManager.clearAllCache();
}

// 导出模块接口
module.exports = {
  initToolsData,
  getCategories,
  getToolsByCategory,
  getAllToolsData,
  getToolById,
  getImageCacheStats,
  clearImageCache,
};
