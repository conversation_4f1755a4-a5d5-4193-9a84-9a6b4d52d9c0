/**
 * 七牛云数据管理器
 * 直接从七牛云CDN获取AI工具数据
 */

// 引入配置
const config = require("../config/config");
const { DATA_VERSION } = config;
// 引入图片缓存管理器
const imageCacheManager = require("./imageCacheManager");

// 内存中的数据缓存
let memoryCache = null;

// 数据版本日期
const DATA_VERSION_DATE = DATA_VERSION.DATE;

// 七牛云配置
const QINIU_CONFIG = config.DATA_SOURCE.QINIU;
const QINIU_BASE_URL = `https://${QINIU_CONFIG.DOMAIN}`;

console.log(`七牛云数据管理器初始化，使用数据版本: ${DATA_VERSION_DATE}`);
console.log(`七牛云域名: ${QINIU_CONFIG.DOMAIN}`);
console.log(`工具JSON路径: ${QINIU_CONFIG.TOOLS_JSON_PATH}`);
console.log(`图片基础路径: ${QINIU_CONFIG.IMAGES_BASE_PATH}`);

/**
 * 初始化工具数据
 * @returns {Promise<boolean>} 是否初始化成功
 */
function initToolsData() {
  console.log(
    `开始从七牛云加载JSON数据，版本: ${DATA_VERSION_DATE}...`
  );

  return new Promise((resolve, reject) => {
    if (memoryCache) {
      console.log("使用内存缓存数据");
      resolve(true);
      return;
    }

    fetchQiniuData()
      .then((success) => {
        resolve(success);
      })
      .catch((error) => {
        console.error(
          `从七牛云获取JSON数据失败，版本: ${DATA_VERSION_DATE}:`,
          error
        );
        reject(error);
      });
  });
}

/**
 * 从七牛云获取JSON数据
 * @returns {Promise<boolean>} 是否获取成功
 */
async function fetchQiniuData() {
  try {
    console.log(`正在从七牛云获取JSON数据，版本: ${DATA_VERSION_DATE}...`);

    const jsonUrl = `${QINIU_BASE_URL}/${QINIU_CONFIG.TOOLS_JSON_PATH}`;
    console.log(`请求URL: ${jsonUrl}`);

    // 使用微信小程序的网络请求API
    const result = await new Promise((resolve, reject) => {
      wx.request({
        url: jsonUrl,
        method: 'GET',
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${res.errMsg}`));
          }
        },
        fail: (error) => {
          reject(error);
        }
      });
    });

    console.log(`✅ 成功获取七牛云JSON数据，版本: ${DATA_VERSION_DATE}`);

    if (
      !result.categories ||
      !result.tools ||
      !Array.isArray(result.categories) ||
      !Array.isArray(result.tools)
    ) {
      console.error("❌ JSON数据格式不正确");
      console.error("数据结构:", {
        hasCategories: !!result.categories,
        categoriesIsArray: Array.isArray(result.categories),
        categoriesLength: result.categories?.length,
        hasTools: !!result.tools,
        toolsIsArray: Array.isArray(result.tools),
        toolsLength: result.tools?.length,
      });
      return false;
    }

    // 处理数据为所需格式
    const processedData = await processData(result.categories, result.tools);

    if (!processedData) {
      console.error("❌ 数据处理失败");
      return false;
    }

    // 更新内存缓存
    memoryCache = processedData;
    console.log(`✅ 七牛云数据加载完成，版本: ${DATA_VERSION_DATE}`);

    return true;
  } catch (error) {
    console.error(
      `❌ 从七牛云获取数据失败，版本: ${DATA_VERSION_DATE}:`,
      error
    );
    console.error("错误详情:", {
      name: error.name,
      message: error.message,
      stack: error.stack,
    });
    return false;
  }
}

/**
 * 处理数据为所需格式，并集成图片缓存
 * @param {Array} categories - 分类数据
 * @param {Array} tools - 工具数据
 * @returns {Object} 处理后的数据
 */
async function processData(categories, tools) {
  try {
    if (!Array.isArray(categories) || !Array.isArray(tools)) {
      console.error("数据格式不正确");
      return null;
    }

    console.log(`[QiniuManager] 开始处理数据，工具数量: ${tools.length}`);

    // 第一步：处理工具数据，转换图片路径为七牛云URL
    const processedTools = tools.map((tool) => {
      const processedTool = { ...tool };

      // 如果有localIcon，转换为七牛云URL
      if (tool.localIcon) {
        const qiniuIconUrl = `${QINIU_BASE_URL}/${QINIU_CONFIG.BASE_PATH}/${tool.localIcon}`;
        processedTool.icon = qiniuIconUrl;
        processedTool.qiniuIcon = qiniuIconUrl;
      }

      return processedTool;
    });

    // 第二步：使用图片缓存管理器处理图片
    const finalTools = await processBatchImagesWithCache(processedTools);

    // 按分类组织工具数据
    const toolsData = {};

    categories.forEach((category) => {
      const categoryId = category.id;
      // 找出属于该分类的所有工具
      toolsData[categoryId] = finalTools.filter(
        (tool) => tool.categoryId === categoryId
      );
    });

    console.log(`七牛云数据处理完成，版本: ${DATA_VERSION_DATE}, 信息汇总:`);
    console.log("- 分类数量:", categories.length);
    console.log("- 总工具数量:", finalTools.length);

    return {
      categories,
      toolsData,
      allTools: finalTools,
    };
  } catch (error) {
    console.error("处理七牛云数据出错:", error);
    return null;
  }
}

/**
 * 批量处理图片缓存
 * @param {Array} tools - 工具数据数组
 * @returns {Array} 处理后的工具数据
 */
async function processBatchImagesWithCache(tools) {
  if (!config.IMAGE_CACHE.ENABLED) {
    console.log("[QiniuManager] 图片缓存已禁用，跳过缓存处理");
    return tools;
  }

  try {
    // 批量处理所有图片URL
    const imageUrls = tools.map((tool) => tool.icon);
    const cachedImagePaths = await imageCacheManager.getBatchImages(imageUrls);

    // 更新工具数据中的图片路径
    const processedTools = tools.map((tool, index) => {
      const cachedPath = cachedImagePaths[index];
      if (cachedPath && cachedPath !== tool.icon) {
        console.log(
          `[QiniuManager] 使用缓存图片: ${tool.name} -> ${cachedPath}`
        );
        tool.icon = cachedPath;
        tool.isCached = true;
      }
      return tool;
    });

    console.log(`[QiniuManager] 图片缓存处理完成`);

    // 定期清理缓存大小
    setTimeout(() => {
      imageCacheManager.manageCacheSize();
    }, 1000);

    return processedTools;
  } catch (error) {
    console.error("[QiniuManager] 批量处理图片缓存失败:", error);
    return tools;
  }
}

/**
 * 获取所有分类数据
 * @returns {Promise<Array>} 分类数据
 */
function getCategories() {
  return new Promise((resolve, reject) => {
    initToolsData()
      .then(() => {
        if (!memoryCache || !memoryCache.categories) {
          reject(new Error("分类数据不可用"));
          return;
        }
        resolve(memoryCache.categories);
      })
      .catch(reject);
  });
}

/**
 * 根据分类ID获取工具数据
 * @param {string} categoryId - 分类ID
 * @returns {Promise<Array>} 工具数据
 */
function getToolsByCategory(categoryId) {
  return new Promise((resolve, reject) => {
    initToolsData()
      .then(() => {
        if (!memoryCache || !memoryCache.toolsData) {
          reject(new Error("工具数据不可用"));
          return;
        }

        const tools = memoryCache.toolsData[categoryId] || [];
        resolve(tools);
      })
      .catch(reject);
  });
}

/**
 * 获取所有工具数据
 * @returns {Promise<Object>} 所有工具数据
 */
function getAllToolsData() {
  return new Promise((resolve, reject) => {
    initToolsData()
      .then(() => {
        if (!memoryCache) {
          reject(new Error("工具数据不可用"));
          return;
        }
        resolve(memoryCache);
      })
      .catch(reject);
  });
}

/**
 * 根据工具ID获取工具详情
 * @param {string} toolId - 工具ID
 * @returns {Promise<Object>} 工具详情
 */
function getToolById(toolId) {
  console.log(`获取工具 ${toolId} 的详情...`);

  return new Promise((resolve, reject) => {
    initToolsData()
      .then(() => {
        if (!memoryCache || !memoryCache.allTools) {
          console.error("工具数据不可用，memoryCache:", memoryCache);
          reject(new Error("工具数据不可用"));
          return;
        }

        // 根据工具ID查找工具详情
        const tool = memoryCache.allTools.find((item) => item.id === toolId);

        if (!tool) {
          console.error(`未找到ID为 ${toolId} 的工具`);
          reject(new Error(`未找到工具: ${toolId}`));
          return;
        }

        console.log(`返回工具 ${toolId} 的详情:`, tool);
        resolve(tool);
      })
      .catch(reject);
  });
}

/**
 * 获取图片缓存统计信息
 * @returns {Object} 缓存统计
 */
function getImageCacheStats() {
  return imageCacheManager.getCacheStats();
}

/**
 * 清理图片缓存
 * @returns {Promise} 清理结果
 */
function clearImageCache() {
  return imageCacheManager.clearAllCache();
}

// 导出模块接口
module.exports = {
  initToolsData,
  getCategories,
  getToolsByCategory,
  getAllToolsData,
  getToolById,
  getImageCacheStats,
  clearImageCache,
};
