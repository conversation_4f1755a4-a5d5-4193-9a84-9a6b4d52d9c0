/**
 * 本地数据管理器
 * 用于从本地文件系统获取AI工具数据（开发调试阶段使用）
 */

// 引入配置
const config = require("../config/config");
const { DATA_VERSION } = config;

// 内存中的数据缓存
let memoryCache = null;

// JSON文件在本地文件系统中的路径
const TOOLS_JSON_PATH = config.DATA_SOURCE.LOCAL.TOOLS_JSON_PATH;
const IMAGES_BASE_PATH = config.DATA_SOURCE.LOCAL.IMAGES_BASE_PATH;
const DATA_VERSION_DATE = DATA_VERSION.DATE;

console.log(`本地数据管理器初始化，使用数据版本: ${DATA_VERSION_DATE}`);
console.log(`工具JSON路径: ${TOOLS_JSON_PATH}`);
console.log(`图片基础路径: ${IMAGES_BASE_PATH}`);

/**
 * 初始化工具数据
 * @returns {Promise<boolean>} 是否初始化成功
 */
function initToolsData() {
  console.log(
    `开始从本地加载JSON数据，版本: ${DATA_VERSION_DATE}, 路径: ${TOOLS_JSON_PATH}...`
  );

  return new Promise((resolve, reject) => {
    if (memoryCache) {
      console.log("使用内存缓存数据");
      resolve(true);
      return;
    }

    fetchLocalData()
      .then((success) => {
        resolve(success);
      })
      .catch((error) => {
        console.error("从本地获取JSON数据失败:", error);
        reject(error);
      });
  });
}

/**
 * 从本地文件系统获取JSON数据
 * @returns {Promise<boolean>} 是否获取成功
 */
async function fetchLocalData() {
  try {
    console.log(`正在读取本地JSON数据文件，版本: ${DATA_VERSION_DATE}...`);

    // 返回Promise包装的异步文件读取
    return new Promise((resolve, reject) => {
      // 使用小程序API读取本地文件
      const fileSystemManager = wx.getFileSystemManager();

      // 使用配置中的路径，无需额外处理
      const filePath = TOOLS_JSON_PATH;

      console.log(`读取文件路径: ${filePath}`);

      fileSystemManager.readFile({
        filePath: filePath,
        encoding: "utf-8",
        success: (res) => {
          try {
            console.log(`成功读取本地JSON文件: ${filePath}`);
            const data = JSON.parse(res.data);

            if (
              !data.categories ||
              !data.tools ||
              !Array.isArray(data.categories) ||
              !Array.isArray(data.tools)
            ) {
              console.error("JSON数据格式不正确");
              reject(new Error("JSON数据格式不正确"));
              return;
            }

            // 处理数据为所需格式
            const processedData = processData(data.categories, data.tools);

            if (!processedData) {
              console.error("数据处理失败");
              reject(new Error("数据处理失败"));
              return;
            }

            // 更新内存缓存
            memoryCache = processedData;
            console.log("数据加载完成");

            resolve(true);
          } catch (parseError) {
            console.error("解析JSON数据失败:", parseError);
            reject(parseError);
          }
        },
        fail: (error) => {
          console.error(`读取文件失败，路径: ${filePath}, 错误:`, error);
          reject(error);
        },
      });
    });
  } catch (error) {
    console.error("读取本地数据失败:", error);
    throw error;
  }
}

/**
 * 处理数据为所需格式
 * @param {Array} categories - 分类数据
 * @param {Array} tools - 工具数据
 * @returns {Object} 处理后的数据
 */
function processData(categories, tools) {
  try {
    if (!Array.isArray(categories) || !Array.isArray(tools)) {
      console.error("数据格式不正确");
      return null;
    }

    // 从配置中获取本地基础路径
    const basePath = config.DATA_SOURCE.LOCAL.BASE_PATH;
    const placeholderIcon = "/images/placeholder/tool-icon.png"; // 定义占位符图标

    tools = tools.map((tool) => {
      // 优先处理 localIcon (本地模式)
      if (tool.localIcon) {
        // 确保 basePath 不以 '/' 结尾，localIcon 不以 '/' 开头，避免双斜杠
        const cleanBasePath = basePath.endsWith("/")
          ? basePath.slice(0, -1)
          : basePath;
        const cleanLocalIcon = tool.localIcon.startsWith("/")
          ? tool.localIcon.slice(1)
          : tool.localIcon;

        tool.icon = `${cleanBasePath}/${cleanLocalIcon}`; // 拼接得到完整路径
        // console.log(`使用 localIcon 构建路径: ${tool.icon}`); // 可选的调试日志
      }
      // 如果 localIcon 不存在，再检查原始的 icon 字段是否为云路径 (保留兼容性)
      else if (tool.icon && tool.icon.indexOf("cloud://") === 0) {
        // 保留原有的云路径处理逻辑（虽然当前JSON中icon为空）
        // 从云存储路径提取文件名
        const iconPathParts = tool.icon.split("/");
        const fileName = iconPathParts[iconPathParts.length - 1];
        const category = tool.categoryId || "未分类";

        // 构建本地图标路径，使用配置中的版本日期
        // 使用配置中的图片基础路径，无需额外处理
        const imagesBasePath = IMAGES_BASE_PATH;
        // 这里的拼接逻辑可能需要根据云存储结构和本地映射关系精确调整
        tool.icon = `${imagesBasePath}/${category}/${fileName}`;
        console.log(
          `[兼容] 图标路径已转换 [版本: ${DATA_VERSION_DATE}]: ${tool.icon}`
        );
      }
      // 如果 localIcon 和 cloud:// 路径都没有，使用占位符
      else {
        tool.icon = placeholderIcon;
        // console.log(`使用占位符图标: ${tool.icon}`); // 可选的调试日志
      }

      return tool;
    });

    // 按分类组织工具数据
    const toolsData = {};

    categories.forEach((category) => {
      const categoryId = category.id;
      // 找出属于该分类的所有工具
      toolsData[categoryId] = tools.filter(
        (tool) => tool.categoryId === categoryId
      );
    });

    console.log(`数据处理完成，版本: ${DATA_VERSION_DATE}, 信息汇总:`);
    console.log("- 分类数量:", categories.length);
    console.log("- 总工具数量:", tools.length);

    return {
      categories,
      toolsData,
      allTools: tools,
    };
  } catch (error) {
    console.error("处理数据出错:", error);
    return null;
  }
}

/**
 * 获取所有工具分类
 * @returns {Promise<Array>} 分类列表
 */
function getCategories() {
  console.log("获取分类数据...");

  return new Promise((resolve, reject) => {
    initToolsData()
      .then(() => {
        if (!memoryCache || !memoryCache.categories) {
          console.error("分类数据不可用，memoryCache:", memoryCache);
          reject(new Error("分类数据不可用"));
          return;
        }
        console.log("返回分类数据，数量:", memoryCache.categories.length);
        resolve(memoryCache.categories);
      })
      .catch(reject);
  });
}

/**
 * 根据分类ID获取工具列表
 * @param {string} categoryId - 分类ID
 * @returns {Promise<Array>} 工具列表
 */
function getToolsByCategory(categoryId) {
  console.log(`获取分类 ${categoryId} 的工具数据...`);

  return new Promise((resolve, reject) => {
    initToolsData()
      .then(() => {
        console.log("数据初始化完成，开始获取指定分类工具");
        const tools = getToolsByCategoryFromCache(categoryId);
        console.log(`返回分类 ${categoryId} 的工具数据，数量:`, tools.length);
        if (tools.length > 0) {
          console.log("工具示例:", tools[0]);
        }
        resolve(tools);
      })
      .catch((error) => {
        console.error(`获取分类 ${categoryId} 工具数据失败:`, error);
        reject(error);
      });
  });
}

/**
 * 从缓存中获取指定分类的工具列表
 * @param {string} categoryId - 分类ID
 * @returns {Array} 工具列表
 */
function getToolsByCategoryFromCache(categoryId) {
  if (!memoryCache || !memoryCache.toolsData) {
    console.error("工具数据不可用，memoryCache:", memoryCache);
    return [];
  }

  const tools = memoryCache.toolsData[categoryId] || [];
  console.log(`从缓存中获取分类 ${categoryId} 的工具，找到 ${tools.length} 个`);
  return tools;
}

/**
 * 获取所有工具数据
 * @returns {Promise<Array>} 所有工具列表
 */
function getAllToolsData() {
  console.log("获取所有工具数据...");

  return new Promise((resolve, reject) => {
    initToolsData()
      .then(() => {
        if (!memoryCache || !memoryCache.allTools) {
          console.error("工具数据不可用，memoryCache:", memoryCache);
          reject(new Error("工具数据不可用"));
          return;
        }
        console.log("返回所有工具数据，数量:", memoryCache.allTools.length);
        resolve(memoryCache.allTools);
      })
      .catch(reject);
  });
}

/**
 * 根据工具ID获取工具详情
 * @param {string} toolId - 工具ID
 * @returns {Promise<Object>} 工具详情
 */
function getToolById(toolId) {
  console.log(`获取工具 ${toolId} 的详情...`);

  return new Promise((resolve, reject) => {
    initToolsData()
      .then(() => {
        if (!memoryCache || !memoryCache.allTools) {
          console.error("工具数据不可用，memoryCache:", memoryCache);
          reject(new Error("工具数据不可用"));
          return;
        }

        // 根据工具ID查找工具详情
        const tool = memoryCache.allTools.find((item) => item.id === toolId);

        if (!tool) {
          console.error(`未找到ID为 ${toolId} 的工具`);
          reject(new Error(`未找到工具: ${toolId}`));
          return;
        }

        console.log(`返回工具 ${toolId} 的详情:`, tool);
        resolve(tool);
      })
      .catch(reject);
  });
}

// 导出模块接口
module.exports = {
  initToolsData,
  getCategories,
  getToolsByCategory,
  getAllToolsData,
  getToolById,
};
