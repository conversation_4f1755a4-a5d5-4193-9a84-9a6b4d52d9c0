/**
 * 图片缓存管理器
 * 用于云端模式下的图片持久化缓存
 */

const config = require("../config/config");
const { IMAGE_CACHE } = config;

class ImageCacheManager {
  constructor() {
    this.fileSystemManager = wx.getFileSystemManager();
    this.cacheDir = `${wx.env.USER_DATA_PATH}/${IMAGE_CACHE.CACHE_DIR_NAME}`;
    this.metaDataKey = IMAGE_CACHE.METADATA_KEY;
    this.maxCacheSize = IMAGE_CACHE.MAX_CACHE_SIZE;
    this.cacheExpireDays = IMAGE_CACHE.CACHE_EXPIRE_DAYS;
    this.maxConcurrentDownloads = IMAGE_CACHE.MAX_CONCURRENT_DOWNLOADS;
    this.downloadQueue = []; // 下载队列
    this.activeDownloads = 0; // 当前活跃下载数
    this.enabled = IMAGE_CACHE.ENABLED;
    this.targetSizeRatio = IMAGE_CACHE.CLEANUP.TARGET_SIZE_RATIO;
    this.placeholderImage = IMAGE_CACHE.PLACEHOLDER_IMAGE;

    if (this.enabled) {
      this.init();
    }
  }

  /**
   * 初始化缓存目录和元数据
   */
  init() {
    try {
      // 确保缓存目录存在
      this.fileSystemManager.mkdirSync(this.cacheDir, true);
      console.log("[ImageCache] 缓存目录初始化成功");
    } catch (error) {
      // 改进错误识别逻辑，匹配多种可能的错误信息格式
      const errorMsg = error.errMsg || error.message || String(error);
      if (
        errorMsg.includes("file already exists") ||
        errorMsg.includes("already exists")
      ) {
        console.log("[ImageCache] 缓存目录已存在，跳过创建");
      } else {
        console.error("[ImageCache] 缓存目录初始化失败:", error);
        // 目录创建失败，禁用缓存功能
        this.enabled = false;
        console.warn("[ImageCache] 由于目录创建失败，缓存功能已禁用");
        return;
      }
    }

    // 根据配置决定是否自动清理过期缓存
    if (IMAGE_CACHE.CLEANUP.AUTO_CLEANUP_ON_START) {
      this.cleanExpiredCache();
    }
  }

  /**
   * 生成缓存文件的键值
   * @param {string} url - 原始图片URL
   * @returns {string} 缓存键值
   */
  generateCacheKey(url) {
    // 使用简单的哈希函数生成唯一键值
    let hash = 0;
    for (let i = 0; i < url.length; i++) {
      const char = url.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 获取缓存元数据
   * @returns {Object} 缓存元数据
   */
  getCacheMetadata() {
    try {
      const metadata = wx.getStorageSync(this.metaDataKey);
      return metadata || {};
    } catch (error) {
      console.error("[ImageCache] 获取缓存元数据失败:", error);
      return {};
    }
  }

  /**
   * 保存缓存元数据
   * @param {Object} metadata - 缓存元数据
   */
  saveCacheMetadata(metadata) {
    try {
      wx.setStorageSync(this.metaDataKey, metadata);
    } catch (error) {
      console.error("[ImageCache] 保存缓存元数据失败:", error);
    }
  }

  /**
   * 检查缓存是否存在且有效
   * @param {string} url - 图片URL
   * @returns {Promise<string|null>} 缓存文件路径或null
   */
  async checkCache(url) {
    if (!this.enabled) {
      return null;
    }

    const cacheKey = this.generateCacheKey(url);
    const cachePath = `${this.cacheDir}/${cacheKey}`;
    const metadata = this.getCacheMetadata();

    // 检查元数据中是否存在该缓存
    if (!metadata[cacheKey]) {
      return null;
    }

    const cacheInfo = metadata[cacheKey];
    const now = Date.now();
    const expireTime =
      cacheInfo.timestamp + this.cacheExpireDays * 24 * 60 * 60 * 1000;

    // 检查是否过期
    if (now > expireTime) {
      console.log(`[ImageCache] 缓存已过期: ${cacheKey}`);
      await this.removeCache(cacheKey);
      return null;
    }

    // 检查文件是否实际存在
    try {
      this.fileSystemManager.accessSync(cachePath);
      console.log(`[ImageCache] 使用缓存: ${cacheKey}`);

      // 更新访问时间
      cacheInfo.lastAccess = now;
      metadata[cacheKey] = cacheInfo;
      this.saveCacheMetadata(metadata);

      return cachePath;
    } catch (error) {
      console.log(`[ImageCache] 缓存文件不存在: ${cacheKey}`);
      // 文件不存在，清理元数据
      delete metadata[cacheKey];
      this.saveCacheMetadata(metadata);
      return null;
    }
  }

  /**
   * 下载并缓存图片
   * @param {string} url - 图片URL
   * @returns {Promise<string>} 缓存文件路径
   */
  async downloadAndCache(url) {
    if (!this.enabled) {
      return url;
    }

    return new Promise((resolve, reject) => {
      // 添加到下载队列
      this.downloadQueue.push({ url, resolve, reject });
      this.processDownloadQueue();
    });
  }

  /**
   * 处理下载队列
   */
  async processDownloadQueue() {
    if (
      this.activeDownloads >= this.maxConcurrentDownloads ||
      this.downloadQueue.length === 0
    ) {
      return;
    }

    const { url, resolve, reject } = this.downloadQueue.shift();
    this.activeDownloads++;

    try {
      const result = await this.executeDownload(url);
      resolve(result);
    } catch (error) {
      reject(error);
    } finally {
      this.activeDownloads--;
      // 处理队列中的下一个任务
      this.processDownloadQueue();
    }
  }

  /**
   * 执行图片下载
   * @param {string} url - 图片URL
   * @returns {Promise<string>} 缓存文件路径
   */
  async executeDownload(url) {
    const cacheKey = this.generateCacheKey(url);
    const cachePath = `${this.cacheDir}/${cacheKey}`;

    console.log(`[ImageCache] 开始下载图片: ${url}`);

    return new Promise((resolve, reject) => {
      wx.downloadFile({
        url: url,
        success: (res) => {
          if (res.statusCode === 200) {
            try {
              // 将临时文件移动到缓存目录
              this.fileSystemManager.saveFileSync(res.tempFilePath, cachePath);

              // 更新元数据
              const metadata = this.getCacheMetadata();
              metadata[cacheKey] = {
                url: url,
                timestamp: Date.now(),
                lastAccess: Date.now(),
                size: this.getFileSize(cachePath),
              };
              this.saveCacheMetadata(metadata);

              console.log(`[ImageCache] 图片下载并缓存成功: ${cacheKey}`);
              resolve(cachePath);
            } catch (error) {
              console.error(
                `[ImageCache] 保存图片到缓存失败: ${cacheKey}`,
                error
              );
              reject(error);
            }
          } else {
            const error = new Error(`下载失败，状态码: ${res.statusCode}`);
            console.error(`[ImageCache] 图片下载失败: ${url}`, error);
            reject(error);
          }
        },
        fail: (error) => {
          console.error(`[ImageCache] 图片下载失败: ${url}`, error);
          reject(error);
        },
      });
    });
  }

  /**
   * 获取文件大小
   * @param {string} filePath - 文件路径
   * @returns {number} 文件大小（字节）
   */
  getFileSize(filePath) {
    try {
      const stats = this.fileSystemManager.statSync(filePath);
      return stats.size || 0;
    } catch (error) {
      console.error("[ImageCache] 获取文件大小失败:", error);
      return 0;
    }
  }

  /**
   * 获取缓存图片（带自动下载）
   * @param {string} url - 图片URL
   * @returns {Promise<string>} 图片路径（缓存路径或原URL）
   */
  async getImage(url) {
    if (!url || typeof url !== "string") {
      return this.placeholderImage;
    }

    // 如果不是网络URL，直接返回
    if (!url.startsWith("http") && !url.includes("tempFileURL")) {
      return url;
    }

    // 如果缓存未启用，直接返回原URL
    if (!this.enabled) {
      return url;
    }

    try {
      // 检查缓存
      const cachedPath = await this.checkCache(url);
      if (cachedPath) {
        return cachedPath;
      }

      // 缓存不存在，下载并缓存
      const downloadedPath = await this.downloadAndCache(url);
      return downloadedPath;
    } catch (error) {
      console.error("[ImageCache] 获取图片失败:", error);
      // 返回原URL作为备选方案
      return url;
    }
  }

  /**
   * 批量获取图片
   * @param {Array<string>} urls - 图片URL数组
   * @returns {Promise<Array<string>>} 图片路径数组
   */
  async getBatchImages(urls) {
    if (!this.enabled) {
      return urls;
    }

    const promises = urls.map((url) => this.getImage(url));
    return Promise.all(promises);
  }

  /**
   * 移除指定缓存
   * @param {string} cacheKey - 缓存键值
   */
  async removeCache(cacheKey) {
    try {
      const cachePath = `${this.cacheDir}/${cacheKey}`;
      this.fileSystemManager.unlinkSync(cachePath);

      // 更新元数据
      const metadata = this.getCacheMetadata();
      delete metadata[cacheKey];
      this.saveCacheMetadata(metadata);

      console.log(`[ImageCache] 移除缓存成功: ${cacheKey}`);
    } catch (error) {
      console.error(`[ImageCache] 移除缓存失败: ${cacheKey}`, error);
    }
  }

  /**
   * 清理过期缓存
   */
  async cleanExpiredCache() {
    if (!this.enabled) {
      return;
    }

    try {
      const metadata = this.getCacheMetadata();
      const now = Date.now();
      const expireTime = this.cacheExpireDays * 24 * 60 * 60 * 1000;

      let cleanedCount = 0;
      for (const [cacheKey, cacheInfo] of Object.entries(metadata)) {
        if (now - cacheInfo.timestamp > expireTime) {
          await this.removeCache(cacheKey);
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        console.log(`[ImageCache] 清理过期缓存 ${cleanedCount} 个`);
      }
    } catch (error) {
      console.error("[ImageCache] 清理过期缓存失败:", error);
    }
  }

  /**
   * 清理所有缓存
   */
  async clearAllCache() {
    if (!this.enabled) {
      console.log("[ImageCache] 缓存未启用，无需清理");
      return;
    }

    try {
      const metadata = this.getCacheMetadata();

      for (const cacheKey of Object.keys(metadata)) {
        await this.removeCache(cacheKey);
      }

      console.log("[ImageCache] 清理所有缓存完成");
    } catch (error) {
      console.error("[ImageCache] 清理所有缓存失败:", error);
    }
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  getCacheStats() {
    if (!this.enabled) {
      return { count: 0, totalSize: 0, totalSizeMB: "0", enabled: false };
    }

    try {
      const metadata = this.getCacheMetadata();
      const cacheKeys = Object.keys(metadata);

      let totalSize = 0;
      for (const cacheInfo of Object.values(metadata)) {
        totalSize += cacheInfo.size || 0;
      }

      return {
        count: cacheKeys.length,
        totalSize: totalSize,
        totalSizeMB: (totalSize / (1024 * 1024)).toFixed(2),
        enabled: this.enabled,
        maxSizeMB: (this.maxCacheSize / (1024 * 1024)).toFixed(0),
        expireDays: this.cacheExpireDays,
      };
    } catch (error) {
      console.error("[ImageCache] 获取缓存统计失败:", error);
      return {
        count: 0,
        totalSize: 0,
        totalSizeMB: "0",
        enabled: this.enabled,
      };
    }
  }

  /**
   * 检查并管理缓存大小
   */
  async manageCacheSize() {
    if (!this.enabled) {
      return;
    }

    try {
      const stats = this.getCacheStats();

      if (stats.totalSize > this.maxCacheSize) {
        console.log(
          `[ImageCache] 缓存超过限制 ${stats.totalSizeMB}MB，开始清理`
        );

        const metadata = this.getCacheMetadata();

        // 按最后访问时间排序，清理最旧的缓存
        const sortedCaches = Object.entries(metadata).sort(
          ([, a], [, b]) => a.lastAccess - b.lastAccess
        );

        let currentSize = stats.totalSize;
        const targetSize = this.maxCacheSize * this.targetSizeRatio;

        for (const [cacheKey, cacheInfo] of sortedCaches) {
          if (currentSize <= targetSize) break;

          await this.removeCache(cacheKey);
          currentSize -= cacheInfo.size || 0;
        }

        console.log("[ImageCache] 缓存清理完成");
      }
    } catch (error) {
      console.error("[ImageCache] 管理缓存大小失败:", error);
    }
  }

  /**
   * 设置缓存启用状态
   * @param {boolean} enabled - 是否启用
   */
  setEnabled(enabled) {
    this.enabled = enabled;
    console.log(`[ImageCache] 缓存${enabled ? "已启用" : "已禁用"}`);
  }

  /**
   * 获取缓存启用状态
   * @returns {boolean} 是否启用
   */
  isEnabled() {
    return this.enabled;
  }
}

// 创建单例实例
const imageCacheManager = new ImageCacheManager();

module.exports = imageCacheManager;
