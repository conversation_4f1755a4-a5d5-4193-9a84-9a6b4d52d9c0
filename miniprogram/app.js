// app.js
// const toolsDataManager = require("./utils/toolsDataManager");
// const toolsDataManager = require("./utils/qiniuDataManager");
// const toolsDataManager = require("./utils/cloudManager");
// const toolsDataManager = require("./utils/jsonCloudManager");
// const toolsDataManager = require("./utils/cloudFunctionManager");

// 使用数据管理器工厂
const dataManagerFactory = require("./utils/dataManagerFactory");
const toolsDataManager = dataManagerFactory.getDataManager();

// 获取配置
const config = require("./config/config");
const CLOUD_ENV_ID = dataManagerFactory.getCloudEnvId();

App({
  onLaunch: function () {
    // 检查数据源类型
    const dataSourceType = dataManagerFactory.getDataSourceType();
    console.log(`当前数据源类型: ${dataSourceType}`);

    // 如果使用云端数据源，则初始化云环境
    if (dataSourceType === "cloud") {
      if (!wx.cloud) {
        console.error("请使用 2.2.3 或以上的基础库以使用云能力");
      } else {
        try {
          wx.cloud.init({
            // env 参数说明：
            //   env 参数决定接下来小程序发起的云开发调用（wx.cloud.xxx）会默认请求到哪个云环境的资源
            //   此处请填入环境 ID, 环境 ID 可打开云控制台查看
            //   如不填则使用默认环境（第一个创建的环境）
            env: CLOUD_ENV_ID,
            traceUser: true,
          });
          console.log(`云环境初始化成功: ${CLOUD_ENV_ID}`);
        } catch (error) {
          console.error("云环境初始化失败:", error);
        }
      }
    } else {
      console.log("使用本地数据源，跳过云环境初始化");
    }

    // 初始化全局数据
    this.globalData = {
      cloudEnvId: CLOUD_ENV_ID,
      dataSourceType: dataSourceType,
      categories: [],
      userInfo: null,
    };

    // 初始化工具数据
    this.initToolsData();
  },

  initToolsData() {
    // 显示加载提示
    wx.showLoading({
      title: "加载中...",
      mask: true,
    });

    toolsDataManager
      .initToolsData()
      .then((success) => {
        console.log("工具数据初始化", success ? "成功" : "部分失败");

        // 初始化成功后，预加载分类数据
        return toolsDataManager.getCategories();
      })
      .then((categories) => {
        if (!categories || !Array.isArray(categories)) {
          console.error("预加载的分类数据无效");
          return Promise.reject(new Error("分类数据无效"));
        }

        console.log("预加载分类数据成功:", categories.length);
        this.globalData.categories = categories;

        // 隐藏加载提示
        wx.hideLoading();
      })
      .catch((error) => {
        console.error("工具数据初始化失败:", error);

        // 隐藏加载提示并显示错误toast
        wx.hideLoading();
        wx.showToast({
          title: "数据加载失败",
          icon: "none",
          duration: 2000,
        });
      });
  },

  // 获取云环境ID
  getCloudEnvId() {
    return CLOUD_ENV_ID;
  },
});
