/**
 * 小程序配置文件
 */

// 数据版本配置
const DATA_VERSION = {
  // 数据版本日期，格式为YYYYMMDD，用于指定使用哪个日期文件夹的数据
  DATE: "20250427",
};

// 数据源配置
const DATA_SOURCE = {
  // 数据源类型: 'local' (本地)、'cloud' (云端) 或 'qiniu' (七牛云)
  TYPE: "qiniu", // 修改为七牛云
  // 如果七牛云有问题，临时回滚请改为: TYPE: "cloud" 或 TYPE: "local"

  // 云环境配置
  CLOUD: {
    ENV_ID: "cloud1-1g0spyn6cff5aa03",
    // 使用数据版本日期构造路径
    BASE_PATH: `数据${DATA_VERSION.DATE}`,
    TOOLS_JSON_PATH: `数据${DATA_VERSION.DATE}/tools_new_${DATA_VERSION.DATE}.json`,
    IMAGES_BASE_PATH: `数据${DATA_VERSION.DATE}/图片`,
  },

  // 七牛云配置
  QINIU: {
    DOMAIN: "naixi.qiniu.logitnote.com",
    BASE_PATH: `数据${DATA_VERSION.DATE}`,
    TOOLS_JSON_PATH: `数据${DATA_VERSION.DATE}/tools_new_${DATA_VERSION.DATE}.json`,
    IMAGES_BASE_PATH: `数据${DATA_VERSION.DATE}/图片`,
  },

  // 本地文件配置
  LOCAL: {
    // JSON文件路径不带开头斜杠（用于文件系统读取）
    TOOLS_JSON_PATH: `data/数据${DATA_VERSION.DATE}/tools_new_${DATA_VERSION.DATE}.json`,
    // 图片路径带开头斜杠（用于图片显示，相对于小程序根目录的绝对路径）
    BASE_PATH: `/data/数据${DATA_VERSION.DATE}`,
    IMAGES_BASE_PATH: `/data/数据${DATA_VERSION.DATE}/图片`,
  },
};

// 图片缓存配置
const IMAGE_CACHE = {
  // 最大缓存大小（字节）
  MAX_CACHE_SIZE: 50 * 1024 * 1024, // 50MB

  // 缓存过期天数
  CACHE_EXPIRE_DAYS: 7,

  // 最大并发下载数
  MAX_CONCURRENT_DOWNLOADS: 3,

  // 缓存目录名
  CACHE_DIR_NAME: "imageCache",

  // 元数据存储键
  METADATA_KEY: "imageCacheMetadata",

  // 是否启用图片缓存（仅云端模式有效）
  ENABLED: true,

  // 缓存清理策略
  CLEANUP: {
    // 启动时是否自动清理过期缓存
    AUTO_CLEANUP_ON_START: true,

    // 缓存大小超过限制时清理到的目标比例
    TARGET_SIZE_RATIO: 0.8, // 清理到80%
  },

  // 默认占位符图片路径
  PLACEHOLDER_IMAGE: "/images/placeholder/tool-icon.png",
};

module.exports = {
  DATA_SOURCE,
  DATA_VERSION,
  IMAGE_CACHE,
};
