# 数据版本管理与数据源切换功能

本文档描述了小程序的数据版本管理和数据源切换功能，用于在开发/测试环境和生产环境之间切换数据来源，以及管理不同版本的数据。

## 概述

小程序支持两种数据源模式：

- **本地模式**：从小程序项目目录下读取数据文件，用于开发和调试
- **云端模式**：从微信云存储获取数据文件，用于生产环境

同时，小程序还实现了基于日期的数据版本管理，便于切换不同版本的数据集。

## 数据版本配置

数据版本配置位于 `miniprogram/config/config.js` 文件中：

```javascript
// 数据版本配置
const DATA_VERSION = {
  // 数据版本日期，格式为YYYYMMDD，用于指定使用哪个日期文件夹的数据
  DATE: "20250427",
};
```

修改 `DATE` 字段的值即可切换到不同日期版本的数据集。

## 数据源配置

数据源配置也位于 `miniprogram/config/config.js` 文件中：

```javascript
// 数据源配置
const DATA_SOURCE = {
  // 数据源类型: 'local' (本地) 或 'cloud' (云端)
  TYPE: "local",

  // 云环境配置
  CLOUD: {
    ENV_ID: "cloud1-1g0spyn6cff5aa03",
    // 使用数据版本日期构造路径
    BASE_PATH: `数据${DATA_VERSION.DATE}`,
    TOOLS_JSON_PATH: `数据${DATA_VERSION.DATE}/tools_new_${DATA_VERSION.DATE}.json`,
    IMAGES_BASE_PATH: `数据${DATA_VERSION.DATE}/图片`,
  },

  // 本地文件配置
  LOCAL: {
    // 使用数据版本日期构造路径
    BASE_PATH: `/数据${DATA_VERSION.DATE}`,
    TOOLS_JSON_PATH: `/数据${DATA_VERSION.DATE}/tools_new_${DATA_VERSION.DATE}.json`,
    IMAGES_BASE_PATH: `/数据${DATA_VERSION.DATE}/图片`,
  },
};
```

修改 `TYPE` 字段的值即可切换数据源：

- 设置为 `'local'` 使用本地数据源
- 设置为 `'cloud'` 使用云端数据源

## 数据文件结构

所有数据文件都应该放在以日期命名的文件夹中（例如：`数据20250427`）。这样的设计便于使用同一套代码切换不同版本的数据。

### 本地数据文件

本地数据文件存放在 `miniprogram/数据{DATE}/` 目录下：

- `tools_new_{DATE}.json`：包含所有工具分类和工具数据
- `图片/` 目录：按分类存放工具图标

其中 `{DATE}` 是当前配置中的版本日期，例如 `20250427`。

### 云端数据文件

云端数据存放在云存储的相同结构目录中：

- `数据{DATE}/tools_new_{DATE}.json`：包含所有工具分类和工具数据
- `数据{DATE}/图片/` 目录：按分类存放工具图标

## 如何切换数据版本

要切换使用的数据版本，只需修改 `config.js` 中的 `DATA_VERSION.DATE` 值：

```javascript
// 数据版本配置
const DATA_VERSION = {
  // 更新为新版本的日期
  DATE: "20250430", // 例如切换到4月30日的数据版本
};
```

只需确保相应的文件夹（`数据20250430`）及其内容已经存在于本地开发环境或云存储中。

## 从开发环境迁移到生产环境

开发完成后，将本地数据迁移到云端的步骤：

1. 确保本地数据文件夹（例如 `miniprogram/数据20250427`）已经调试完成
2. 将整个数据文件夹（`数据20250427`）上传到云存储的根目录，保持完全相同的目录结构
3. 修改 `miniprogram/config/config.js` 中的 `TYPE` 为 `'cloud'`
4. 确保 `DATA_VERSION.DATE` 值与上传的文件夹日期一致
5. 重新编译并发布小程序

## 更新数据版本的流程

当需要更新数据时，应该创建一个新的日期文件夹，而不是修改现有的文件夹：

1. 在本地创建新的数据文件夹（例如 `miniprogram/数据20250430`）
2. 将最新版本的 JSON 数据文件命名为 `tools_new_20250430.json`
3. 在新文件夹中创建 `图片` 子目录，按分类存放图标
4. 开发和测试完成后，将整个新文件夹上传到云存储
5. 修改 `DATA_VERSION.DATE` 为新的日期值（`20250430`）

这种方式可以：

- 保留旧版本数据，方便随时回滚
- 避免更新过程中的数据不一致问题
- 简化部署流程，只需上传一个文件夹

## 框架原理

数据版本管理和数据源切换功能通过以下几个关键文件实现：

- `miniprogram/config/config.js`：配置文件，定义数据版本日期和数据源类型
- `miniprogram/utils/dataManagerFactory.js`：数据管理器工厂，根据配置选择数据源
- `miniprogram/utils/localDataManager.js`：本地数据管理器，处理本地文件读取
- `miniprogram/utils/cloudFunctionManager.js`：云端数据管理器，处理云函数调用
- `cloudfunctions/getJsonData/index.js`：云函数，支持基于日期版本的路径访问
