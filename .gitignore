# 微信小程序忽略文件配置

# 开发工具本地配置
project.config.json
project.private.config.json

# 编译生成的目录
miniprogram_npm/
node_modules/
dist/

# 本地环境文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 编辑器目录和文件
.idea/
.vscode/
*.swp
*.swo
.DS_Store
*/.DS_Store

# 日志文件
npm-debug.log*
yarn-debug.log*
yarn-error.log*
logs/
*.log

# 临时文件
tmp/
temp/

# 微信开发者工具本地设置
.tea/

# 编译输出文件
*.js.map

# 小程序模拟器缓存
simulator/

# 微信小程序特定
sitemap.json

# 备份文件
*.bak 